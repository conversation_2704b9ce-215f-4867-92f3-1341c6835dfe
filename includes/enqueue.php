<?php

const DD_THEME_VERSION = '2.3.0';

function dd_load_stylesheets() {

  wp_register_style('global', get_template_directory_uri() . '/assets/css/global.css', array(), DD_THEME_VERSION);
  wp_enqueue_style('global');

  wp_register_style('style', get_template_directory_uri() . '/assets/css/style.css', array(), DD_THEME_VERSION);
  wp_enqueue_style('style');

  wp_register_style( 'swiper', get_template_directory_uri() . '/assets/css/swiper.css', array(), DD_THEME_VERSION);
	wp_enqueue_style( 'swiper' );

}

add_action('wp_enqueue_scripts', 'dd_load_stylesheets');

function dd_load_js() {

  wp_deregister_script( 'jquery' );

	wp_register_script( 'jquery', get_template_directory_uri() . '/assets/js/jquery.js', array(), DD_THEME_VERSION, true);
	wp_enqueue_script( 'jquery' );

  wp_register_script( 'swiper', get_template_directory_uri() . '/assets/js/swiper.js', array(), DD_THEME_VERSION);
	wp_enqueue_script( 'swiper' );

  wp_register_script('script', get_template_directory_uri() . '/assets/js/script.js', array(), DD_THEME_VERSION, true);
  wp_enqueue_script('script');



}

add_action('wp_enqueue_scripts', 'dd_load_js');

