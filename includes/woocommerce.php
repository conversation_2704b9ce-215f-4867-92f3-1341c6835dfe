<?php

function fitospa_woocommerce_support() {
    add_theme_support('woocommerce');
}
add_action('after_setup_theme', 'fitospa_woocommerce_support');

remove_action( 'woocommerce_before_main_content', 'woocommerce_breadcrumb', 20, 0 );

function fitospa_woocommerce_image_dimensions() {
    $catalog = array(
        'width'  => '300',   // px
        'height' => '300',   // px
        'crop'   => 1        // true
    );
    $single = array(
        'width'  => '600',   // px
        'height' => '600',   // px
        'crop'   => 1        // true
    );
    $thumbnail = array(
        'width'  => '150',   // px
        'height' => '150',   // px
        'crop'   => 1        // true
    );

    // Update the WooCommerce image sizes
    update_option('woocommerce_catalog_image_size', $catalog);
    update_option('woocommerce_single_image_size', $single);
    update_option('woocommerce_thumbnail_image_size', $thumbnail);
}
add_action('after_setup_theme', 'fitospa_woocommerce_image_dimensions', 20);

add_action('wp_ajax_woocommerce_ajax_add_to_cart', 'woocommerce_ajax_add_to_cart_handler');
add_action('wp_ajax_nopriv_woocommerce_ajax_add_to_cart', 'woocommerce_ajax_add_to_cart_handler');

function woocommerce_ajax_add_to_cart_handler() {
    if ( !isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'woocommerce-add-to-cart') ) {
        wp_send_json_error( array('error' => true, 'message' => 'Nonce verification failed') );
        wp_die();
    }

    if ( !isset($_POST['product_id']) || !isset($_POST['quantity']) ) {
        wp_send_json_error( array('error' => true, 'message' => 'Product ID or quantity not set') );
        wp_die();
    }

    $product_id = intval($_POST['product_id']);
    $quantity = intval($_POST['quantity']);

    $added = WC()->cart->add_to_cart($product_id, $quantity);

    if ($added) {
        WC()->cart->calculate_totals();
        WC()->cart->maybe_set_cart_cookies();

        wp_send_json_success( array(
            'cart_count' => WC()->cart->get_cart_contents_count(),
            'cart_url' => wc_get_cart_url()
        ));
    } else {
        wp_send_json_error( array('error' => true, 'message' => 'Failed to add product to cart') );
    }

    wp_die();
}
add_action( 'wp_ajax_woocommerce_remove_cart_item', 'remove_cart_item_ajax' );
add_action( 'wp_ajax_nopriv_woocommerce_remove_cart_item', 'remove_cart_item_ajax' );

function remove_cart_item_ajax() {
    $cart_item_key = sanitize_text_field( $_POST['cart_item_key'] );
    $removed = WC()->cart->remove_cart_item( $cart_item_key );

    if ( $removed ) {
        wp_send_json_success();
    } else {
        wp_send_json_error();
    }
}

function ensure_woocommerce_error_messages_display() {
    if ( ! is_user_logged_in() ) {
        wc_print_notices(); // Wyświetlenie błędów WooCommerce
    }
}
add_action( 'woocommerce_before_customer_login_form', 'ensure_woocommerce_error_messages_display' );

add_action( 'wp_ajax_apply_checkout_coupon', 'apply_checkout_coupon_ajax_receiver' );
add_action( 'wp_ajax_nopriv_apply_checkout_coupon', 'apply_checkout_coupon_ajax_receiver' );
function apply_checkout_coupon_ajax_receiver() {
    if ( isset($_POST['coupon_code']) && ! empty($_POST['coupon_code']) ) {
        WC()->cart->add_discount( wc_format_coupon_code( wp_unslash( $_POST['coupon_code'] ) ) ); // phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotSanitized
    } else {
        wc_add_notice( WC_Coupon::get_generic_coupon_error( WC_Coupon::E_WC_COUPON_PLEASE_ENTER ), 'error' );
    }
    wc_print_notices();
    wp_die();
}

add_filter( 'wpcf7_validate_email*', 'dd_cf7_validation_newsletter', 20, 2 );

function dd_cf7_validation_newsletter( $result, $tag ) {
    if (isset($_POST['dd-newsletter']) && !isset($_POST['gr_marketing_consent'])) {
        $result->invalidate($tag, 'Musisz zaakceptować zgodę na przetwarzanie danych osobowych!');
    }

    return $result;
}

add_action('wpcf7_mail_sent', 'dd_cf7_newsletter_send_coupon');

function dd_cf7_newsletter_send_coupon($cf7)
{
    if (!isset($_POST['dd-newsletter'])) {
        return;
    }

    /** Generate coupon */
    $date_expires     = date('Y-m-d', strtotime('+1 month'));
    $discount_type    = 'percent';
    $coupon_amount    = '15';

    $coupon = new WC_Coupon();

    $code = substr(str_shuffle(str_repeat('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ', 10)), 0, 10);

    $coupon->set_code($code);
    //the coupon discount type can be 'fixed_cart', 'percent' or 'fixed_product', defaults to 'fixed_cart'
    $coupon->set_discount_type($discount_type);
    //the discount amount, defaults to zero
    $coupon->set_amount($coupon_amount );
    $coupon->set_date_expires( $date_expires );
    $coupon->set_usage_limit(1);

    //save the coupon
    $coupon->save();

    /** Send e-mail */
    // load the mailer class
    $mailer = WC()->mailer();

    //format the email
    $recipient = sanitize_text_field($_POST['email']);
    $subject = 'Potwierdzenie dołączenia do newsletter Fitospa';
    $content = wc_get_template_html(
            'emails/customer-newsletter-coupon.php',
        [
            'email_heading' => $subject,
            'sent_to_admin' => false,
            'plain_text'    => false,
            'email'         => $mailer,
            'code' => $code,
        ]
    );
    $headers = "Content-Type: text/html\r\n";

    //send the email through wordpress
    $mailer->send( $recipient, $subject, $content, $headers );
}

add_filter('wpcf7_autop_or_not', '__return_false');

/** Remove product data tabs */

add_filter( 'woocommerce_product_tabs', function ( $tabs ) {
    unset( $tabs['additional_information'] );
    return $tabs;
}, 98 );

add_filter( 'woocommerce_product_description_heading', '__return_null' );

add_action('woocommerce_cart_calculate_fees', 'dd_add_cod_shipping_fee');

function dd_add_cod_shipping_fee() {
    if (is_admin() && !defined('DOING_AJAX')) {
        return;
    }

    // Sprawdź czy wybrana jest płatność za pobraniem
    $chosen_payment_method = WC()->session->get('chosen_payment_method');

    if ($chosen_payment_method !== 'cod') {
        return;
    }

    // Pobierz obecny koszt wysyłki
    $shipping_total = WC()->cart->get_shipping_total();
    $shipping_cod_fee = (DD_COD_SHIPPING_COST - $shipping_total);

    // Dodaj opłatę za pobraniem
    WC()->cart->add_fee('Opłata za płatność za pobraniem', $shipping_cod_fee, true);
}
