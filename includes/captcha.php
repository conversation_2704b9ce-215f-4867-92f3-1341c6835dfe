<?php

/**
 * Simple Math Captcha for Registration Form
 * 
 * This file contains functions to generate and validate simple math captcha
 * for the WooCommerce registration form.
 */

/**
 * Generate a new math captcha question
 *
 * @return array Contains 'question', 'captcha_id', and stores answer in transient
 */
function dd_generate_math_captcha() {
    try {
        // Generate two random numbers between 1 and 10
        $num1 = rand(1, 10);
        $num2 = rand(1, 10);
        $answer = $num1 + $num2;

        // Create unique captcha ID
        $captcha_id = 'captcha_' . wp_generate_uuid4();

        // Store the answer in WordPress transient (expires in 15 minutes)
        $transient_set = set_transient($captcha_id, $answer, 15 * MINUTE_IN_SECONDS);

        if (!$transient_set) {
            error_log('Failed to set captcha transient for ID: ' . $captcha_id);
            // Set fallback transient with answer 10
            set_transient('captcha_fallback_' . time(), 10, 15 * MINUTE_IN_SECONDS);
            return array(
                'question' => 'Podaj wynik działania 5 + 5',
                'captcha_id' => 'captcha_fallback_' . time()
            );
        }

        return array(
            'question' => sprintf('Podaj wynik działania %d + %d', $num1, $num2),
            'captcha_id' => $captcha_id
        );
    } catch (Exception $e) {
        error_log('Captcha generation error: ' . $e->getMessage());
        // Return a fallback captcha
        $fallback_id = 'captcha_fallback_' . time();
        set_transient($fallback_id, 10, 15 * MINUTE_IN_SECONDS);
        return array(
            'question' => 'Podaj wynik działania 5 + 5',
            'captcha_id' => $fallback_id
        );
    }
}

/**
 * Validate captcha answer
 *
 * @param string $captcha_id The captcha identifier
 * @param string $user_answer The user's answer
 * @return bool True if answer is correct, false otherwise
 */
function dd_validate_math_captcha($captcha_id, $user_answer) {
    try {
        // Get the correct answer from transient
        $correct_answer = get_transient($captcha_id);

        // If transient doesn't exist (expired or invalid), return false
        if ($correct_answer === false) {
            error_log('Captcha validation failed: transient not found for ID ' . $captcha_id);
            return false;
        }

        // Delete the transient to prevent reuse
        delete_transient($captcha_id);

        // Compare answers (convert to integers for comparison)
        $is_valid = (int)$user_answer === (int)$correct_answer;

        if (!$is_valid) {
            error_log('Captcha validation failed: incorrect answer. Expected: ' . $correct_answer . ', Got: ' . $user_answer);
        }

        return $is_valid;
    } catch (Exception $e) {
        error_log('Captcha validation error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Display captcha field HTML
 * 
 * @param array $captcha_data Array containing question and captcha_id
 * @return string HTML for captcha field
 */
function dd_display_captcha_field($captcha_data) {
    $question = esc_html($captcha_data['question']);
    $captcha_id = esc_attr($captcha_data['captcha_id']);
    
    $html = '<p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide captcha-field">';
    $html .= '<label for="captcha_answer">' . $question . ' <span class="required">*</span></label>';
    $html .= '<input type="number" class="woocommerce-Input woocommerce-Input--text input-text" name="captcha_answer" id="captcha_answer" required aria-required="true" min="2" max="20" />';
    $html .= '<input type="hidden" name="captcha_id" value="' . $captcha_id . '" />';
    $html .= '</p>';
    
    return $html;
}
