<?php

function extend_search_query( $query ) {
    if ( ! is_admin() && $query->is_search() && $query->is_main_query() ) {
        $search_query = $query->get('s');

        // Szukaj tylko produktów
        $query->set('post_type', array('product'));

        // Jeśli zapytanie zawiera słowo kluczowe, dodaj dodatkowe filtry
        if ($search_query) {
            $tax_query = array(
                'relation' => 'OR',
                array(
                    'taxonomy' => 'product_cat',
                    'field'    => 'name',
                    'terms'    => $search_query,
                    'operator' => 'LIKE',
                ),
                array(
                    'taxonomy' => 'product_tag',
                    'field'    => 'name',
                    'terms'    => $search_query,
                    'operator' => 'LIKE',
                ),
            );
            $query->set('tax_query', $tax_query);
        }
    }
    return $query;
}
add_filter('pre_get_posts', 'extend_search_query');

function redirect_to_category_or_tag() {
    if ( ! is_admin() && is_search() && ! have_posts() && ! empty( $_GET['s'] ) ) {
        $search_query = sanitize_text_field( $_GET['s'] );

        // Sprawdź, czy istnieje kategoria z nazwą zapytania
        $category = get_term_by( 'name', $search_query, 'product_cat' );
        if ( $category ) {
            wp_redirect( get_term_link( $category ) );
            exit;
        }

        // Sprawdź, czy istnieje tag z nazwą zapytania
        $tag = get_term_by( 'name', $search_query, 'product_tag' );
        if ( $tag ) {
            wp_redirect( get_term_link( $tag ) );
            exit;
        }

        // Jeśli nie znaleziono kategorii ani tagu, ale istnieją produkty
        $products = new WP_Query( array(
            'post_type' => 'product',
            's'         => $search_query,
            'posts_per_page' => -1
        ) );
        if ( $products->have_posts() ) {
            wp_redirect( home_url( '/search/' . urlencode( $search_query ) ) );
            exit;
        }

        // Jeśli nic nie znaleziono, przekieruj na stronę główną
        wp_redirect( home_url( '/search/' . urlencode( $search_query ) ) );
        exit;
    }
}
add_action( 'template_redirect', 'redirect_to_category_or_tag' );



add_shortcode('dd_blog_posts', function ($atts = [], $content = null, $tag = ''){
    $atts = array_change_key_case( (array) $atts, CASE_LOWER );

    $dd_atts = shortcode_atts(
        array(
            'ids' => '',
        ), $atts, $tag
    );

    $postIds = explode(',', $dd_atts['ids']);

    $html = '<div class="blog-posts">';

    $args = [
        'posts_per_page' => 4,
        'post_type' => 'post',
        'post__in' => $postIds,
    ];

    $query = new WP_Query($args);

      if ($query->have_posts()){
        while ($query->have_posts()) {
          $query->the_post();
            $html .= '<div class="blog-post">
                <div class="post-thumbnail">
                    <a href="' . get_permalink() . '">';
                         if (has_post_thumbnail()) {
                            $html .= '<img src="' . get_the_post_thumbnail_url(get_the_ID(), 'medium') . '">';
                        }
                    $html .= '</a>
                </div>
                <div class="post-info">
                    <h3>
                    <a href="' . get_permalink() . '">' . get_the_title() . '</a>
                    </h3>
                    <span class="post-date">
                     ' . get_the_date() . '
                     </span>
                </div>
            </div>';
        }
        wp_reset_postdata();
        } else {
          $html .= '<p> Brak wpisów do wyświetlenia.</p >';
        }
    $html .= '</div>';

      return $html;
});