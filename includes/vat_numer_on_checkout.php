<?php

/** Obsługa NIP na formularzu zamówienia */
const DD_BILLING_VAT_NUMER_KEY = 'billing_vat_number';
add_action( 'woocommerce_checkout_update_order_meta', 'dd_checkout_vat_number_update_order_meta' );
function dd_checkout_vat_number_update_order_meta( $order_id ) {
    $order = wc_get_order($order_id);
    if ( ! empty( $_POST[DD_BILLING_VAT_NUMER_KEY] ) ) {
        update_post_meta( $order_id, DD_BILLING_VAT_NUMER_KEY, sanitize_text_field( $_POST[DD_BILLING_VAT_NUMER_KEY] ) );
        $order->update_meta_data(DD_BILLING_VAT_NUMER_KEY, sanitize_text_field( $_POST[DD_BILLING_VAT_NUMER_KEY] ));
    }
    $order->save();
}

// Zapisz dane firmy w metadanych użytkownika po złożeniu zamówienia
add_action('woocommerce_checkout_update_order_meta', 'dd_save_vat_and_company_to_user', 10, 1);
function dd_save_vat_and_company_to_user($order_id) {
    $order = wc_get_order($order_id);
    $user_id = $order->get_user_id();

    // Zapisz dane tylko dla zalogowanych użytkowników
    if ($user_id > 0) {
        if (!empty($_POST[DD_BILLING_VAT_NUMER_KEY])) {
            update_user_meta($user_id, DD_BILLING_VAT_NUMER_KEY, sanitize_text_field($_POST[DD_BILLING_VAT_NUMER_KEY]));
        }
    }
}

// Pobierz zapisane dane firmowe dla zalogowanego użytkownika
add_filter('woocommerce_checkout_get_value', 'dd_get_user_company_data', 10, 2);
function dd_get_user_company_data($value, $input) {
    // Tylko dla zalogowanych użytkowników
    if (!is_user_logged_in()) {
        return $value;
    }

    $user_id = get_current_user_id();

    // Sprawdź, o które pole chodzi
    if ($input === DD_BILLING_VAT_NUMER_KEY) {
        return get_user_meta($user_id, DD_BILLING_VAT_NUMER_KEY, true);
    }

    return $value;
}

// Dodaj pola do formularza edycji adresu rozliczeniowego
add_filter('woocommerce_billing_fields', 'dd_add_vat_company_billing_fields');
function dd_add_vat_company_billing_fields($fields) {

    $fields[DD_BILLING_VAT_NUMER_KEY] = array(
        'type'          => 'text',
        'label'         => __('NIP', 'woocommerce'),
        'placeholder'   => __('Wpisz NIP', 'woocommerce'),
        'required'      => false,
        'class'         => array('form-row-wide'),
        'priority'      => 31, // Po polu company_name
    );

    return $fields;
}

add_filter('woocommerce_order_get_formatted_billing_address', 'dd_add_nip_to_address_displaying', 10, 2);

function dd_add_nip_to_address_displaying($address, $order) {
    if (is_admin()) {
        return $address;
    }

    $order_id = wc_get_order_id_by_order_key(wc_clean($_GET['key'] ?? ''));

    if ($order_id <= 0) {
        $current_url = home_url(add_query_arg( null, null));

        preg_match('/view-order\/(\d+)\/?$/', $current_url, $matches);
        $order_id = (int) ($matches[1] ?? 0);
    }

    if ($order_id <= 0) {
        return $address;
    }

    $vat_number = get_post_meta($order_id, DD_BILLING_VAT_NUMER_KEY, true);

    if (empty($vat_number)) {
        return $address;
    }

    $address .= "<p>NIP: $vat_number</p>";

    return $address;
}

// Zapisz zmiany z panelu administracyjnego
add_action('woocommerce_process_shop_order_meta', 'dd_save_admin_order_company_fields', 10, 1);
function dd_save_admin_order_company_fields($order_id) {
    if (isset($_POST[DD_BILLING_VAT_NUMER_KEY])) {
        update_post_meta($order_id, DD_BILLING_VAT_NUMER_KEY, sanitize_text_field($_POST[DD_BILLING_VAT_NUMER_KEY]));
    }
}

add_action( 'woocommerce_admin_order_data_after_billing_address', 'dd_vat_number_display_admin_order_meta', 10, 1 );
/**
 * Wyświetlenie pola NIP
 */
function dd_vat_number_display_admin_order_meta( $order ) {
    echo '<p><strong>' . __( 'NIP', 'woocommerce' ) . ':</strong> ' . get_post_meta( $order->get_id(), DD_BILLING_VAT_NUMER_KEY, true ) . '</p>';
}