<?php

function mytheme_setup() {
    // Dodaj wsparcie dla logo
    add_theme_support('custom-logo', array(
        'width'      => 300,
        'height'     => 100,
        'flex-width' => true,
        'flex-height' => true,
    ));

    // Dodaj wsparcie dla nagłówka
    add_theme_support('custom-header', array(
        'default-image' => get_template_directory_uri() . '/images/header.jpg',
        'width'          => 1200,
        'height'         => 280,
        'flex-width'     => true,
        'flex-height'    => true,
    ));
}
add_action('after_setup_theme', 'mytheme_setup');

function dd_generate_breadcrumbs()
{
    if (function_exists('yoast_breadcrumb')) {
        yoast_breadcrumb('<p class="dd-breadcrumbs">', '</p>');
    }
}

// Funkcja przekierowująca na stronę logowania, jeśli użytkownik nie jest zalogowany
function redirect_user_based_on_login_status() {
    global $wp;

    if ($wp->request === 'moje-konto') {
        if (!is_user_logged_in()) {
            // Użytkownik nie jest zalogowany, przekieruj na stronę logowania
            wp_redirect(home_url('/logowanie'));
            exit;
        }
    }
}
add_action('template_redirect', 'redirect_user_based_on_login_status');

// Funkcja przekierowująca po zalogowaniu na stronę "Moje Konto"
function custom_login_redirect($redirect_to, $requested_redirect_to, $user) {
    if (isset($user->ID)) {
        $redirect_to = home_url('/moje-konto');
    }
    return $redirect_to;
}
add_filter('woocommerce_login_redirect', 'custom_login_redirect', 10, 3);

// Funkcja przekierowująca na stronę logowania po wylogowaniu
function custom_logout_redirect() {
    wp_redirect(home_url('/logowanie'));
    exit();
}
add_action('wp_logout', 'custom_logout_redirect');

// Funkcja do obsługi niestandardowego formularza logowania
function custom_login_process() {
    if (is_admin()) {
        return;
    }

    if (isset($_POST['login']) && !empty($_POST['login'])) {
        $user = wp_signon(array(
            'user_login'    => sanitize_text_field($_POST['username']),
            'user_password' => sanitize_text_field($_POST['password']),
            'remember'      => isset($_POST['rememberme'])
        ), false);

        if (is_wp_error($user)) {
            // Jeśli wystąpił błąd, przekieruj z powrotem do strony logowania z komunikatem o błędzie
            wp_redirect(home_url('/logowanie') . '?login=failed');
            exit;
        } else {
            // Przekieruj na stronę "Moje Konto" po pomyślnym logowaniu
            wp_redirect(home_url('/moje-konto'));
            exit;
        }
    }
}
add_action('init', 'custom_login_process');

// Funkcja do obsługi komunikatów
function handle_registration_messages() {
    if (isset($_GET['registration'])) {
        switch ($_GET['registration']) {
            case 'success':
                wc_add_notice('Rejestracja zakończona sukcesem. Możesz teraz się zalogować.', 'success');
                break;
            case 'failed':
                wc_add_notice('Wystąpił błąd podczas rejestracji. Sprawdź wprowadzone dane.', 'error');
                break;
        }
    }
}
add_action('wp', 'handle_registration_messages');

function dd_theme_frontpage_products() {
    add_theme_support('dd-product-settings');
    add_action('customize_register', function($wp_customize) {
        $wp_customize->add_section('dd_product_settings', [
            'title'       => __('Produkty na stronie głównej', 'fitspa'),
            'priority'    => 30,
        ]);

        $wp_customize->add_setting('dd_products_description', [
            'default'   => '',
            'sanitize_callback' => 'sanitize_text_field',
        ]);
        $wp_customize->add_control('dd_products_description', [
            'section'  => 'dd_product_settings',
            'type'     => 'hidden',
            'label'    => __('Wprowadzenie produktów', 'fitspa'),
            'description' => __('Wprowadź ID produktów dla poszczególnych sekcji, oddzielając je przecinkami.', 'fitspa'),
        ]);

         // Sekcja produktów "Kategoria"
         $wp_customize->add_setting('dd_categories_ids', [
            'default'   => '',
            'sanitize_callback' => 'sanitize_text_field',
        ]);
        $wp_customize->add_control('dd_categories_ids', [
            'label'    => __('Kategorie produktów na stronie głównej', 'fitspa'),
            'section'  => 'dd_product_settings',
            'type'     => 'text',
        ]);

        // "Bestsellery"
        $wp_customize->add_setting('dd_best_sellers', [
            'default'   => '',
            'sanitize_callback' => 'sanitize_text_field',
        ]);
        $wp_customize->add_control('dd_best_sellers', [
            'label'    => __('Produkty w sekcji "Bestsellery"', 'fitspa'),
            'section'  => 'dd_product_settings',
            'type'     => 'text',
        ]);

        // "Coś dla Ciała"
        $wp_customize->add_setting('dd_body_products', [
            'default'   => '',
            'sanitize_callback' => 'sanitize_text_field',
        ]);
        $wp_customize->add_control('dd_body_products', [
            'label'    => __('Produkty w sekcji "Coś dla Ciała"', 'fitspa'),
            'section'  => 'dd_product_settings',
            'type'     => 'text',
        ]);

        // "Coś dla Twarzy"
        $wp_customize->add_setting('dd_face_products', [
            'default'   => '',
            'sanitize_callback' => 'sanitize_text_field',
        ]);
        $wp_customize->add_control('dd_face_products', [
            'label'    => __('Produkty w sekcji "Coś dla Twarzy"', 'fitspa'),
            'section'  => 'dd_product_settings',
            'type'     => 'text',
        ]);

        // "Coś do Kąpieli"
        $wp_customize->add_setting('dd_bath_products', [
            'default'   => '',
            'sanitize_callback' => 'sanitize_text_field',
        ]);
        $wp_customize->add_control('dd_bath_products', [
            'label'    => __('Produkty w sekcji "Coś do Kąpieli"', 'fitspa'),
            'section'  => 'dd_product_settings',
            'type'     => 'text',
        ]);

        // "Inspiracje"
        $wp_customize->add_setting('dd_inspired_products', [
            'default'   => '',
            'sanitize_callback' => 'sanitize_text_field',
        ]);
        $wp_customize->add_control('dd_inspired_products', [
            'label'    => __('Produkty w sekcji "Inspiracje"', 'fitspa'),
            'section'  => 'dd_product_settings',
            'type'     => 'text',
        ]);

        // Zintegrowanie 3 kart w jedną sekcję
        $wp_customize->add_setting('dd_cards_products', [
            'default'   => '',
            'sanitize_callback' => 'sanitize_text_field',
        ]);
        $wp_customize->add_control('dd_cards_products', [
            'label'    => __('Produkty w sekcji "Karty"', 'fitspa'),
            'section'  => 'dd_product_settings',
            'type'     => 'text',
            'description' => __('Wprowadź ID produktów dla trzech kart, oddzielając je średnikami. Format: Karta 1; Karta 2; Karta 3', 'fitspa'),
        ]);
    });
}

add_action('after_setup_theme', 'dd_theme_frontpage_products');