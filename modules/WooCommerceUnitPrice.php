<?php

namespace DD\FitospaTheme;

use WC_Product;

class WooCommerceUnitPrice
{
    private const META_KEY_UNIT_COUNT = '_dd_unit_count';
    private const META_KEY_UNIT_TYPE = '_dd_unit_type';
    
    private const UNIT_TYPE_MASS = 'mass';
    private const UNIT_TYPE_VOLUME = 'volume';

    public static function init(): void
    {
        // Admin hooks for simple products
        add_action('woocommerce_product_options_pricing', [self::class, 'addSimpleProductFields']);
        add_action('woocommerce_process_product_meta', [self::class, 'saveSimpleProductFields']);

        // Admin hooks for variable products
        add_action('woocommerce_variation_options_pricing', [self::class, 'addVariationFields'], 10, 3);
        add_action('woocommerce_save_product_variation', [self::class, 'saveVariationFields'], 10, 2);
    }

    public static function addSimpleProductFields(): void
    {
        echo '<div class="options_group">';
        
        woocommerce_wp_text_input([
            'id' => self::META_KEY_UNIT_COUNT,
            'label' => __('Liczba jednostek', 'woocommerce'),
            'placeholder' => 'np. 30',
            'desc_tip' => true,
            'description' => __('Podaj liczbę jednostek (gramów lub mililitrów)', 'woocommerce'),
            'type' => 'number',
            'custom_attributes' => [
                'step' => '1',
                'min' => '1'
            ]
        ]);

        woocommerce_wp_select([
            'id' => self::META_KEY_UNIT_TYPE,
            'label' => __('Typ jednostek', 'woocommerce'),
            'options' => [
                '' => __('Wybierz typ jednostek', 'woocommerce'),
                self::UNIT_TYPE_MASS => __('Masa (gramy)', 'woocommerce'),
                self::UNIT_TYPE_VOLUME => __('Objętość (mililitry)', 'woocommerce')
            ],
            'desc_tip' => true,
            'description' => __('Wybierz czy produkt jest mierzony w gramach czy mililitrach', 'woocommerce')
        ]);

        echo '</div>';
    }

    public static function saveSimpleProductFields($post_id): void
    {
        $unit_count = isset($_POST[self::META_KEY_UNIT_COUNT]) ? intval($_POST[self::META_KEY_UNIT_COUNT]) : '';
        $unit_type = isset($_POST[self::META_KEY_UNIT_TYPE]) ? sanitize_text_field($_POST[self::META_KEY_UNIT_TYPE]) : '';

        update_post_meta($post_id, self::META_KEY_UNIT_COUNT, $unit_count);
        update_post_meta($post_id, self::META_KEY_UNIT_TYPE, $unit_type);
    }

    public static function addVariationFields($loop, $variation_data, $variation): void
    {
        $unit_count = get_post_meta($variation->ID, self::META_KEY_UNIT_COUNT, true);
        $unit_type = get_post_meta($variation->ID, self::META_KEY_UNIT_TYPE, true);

        echo '<div class="form-row form-row-full">';
        
        woocommerce_wp_text_input([
            'id' => self::META_KEY_UNIT_COUNT . '[' . $loop . ']',
            'name' => self::META_KEY_UNIT_COUNT . '[' . $loop . ']',
            'value' => $unit_count,
            'label' => __('Liczba jednostek', 'woocommerce'),
            'placeholder' => 'np. 30',
            'desc_tip' => true,
            'description' => __('Podaj liczbę jednostek (gramów lub mililitrów)', 'woocommerce'),
            'type' => 'number',
            'custom_attributes' => [
                'step' => '1',
                'min' => '1'
            ]
        ]);

        woocommerce_wp_select([
            'id' => self::META_KEY_UNIT_TYPE . '[' . $loop . ']',
            'name' => self::META_KEY_UNIT_TYPE . '[' . $loop . ']',
            'value' => $unit_type,
            'label' => __('Typ jednostek', 'woocommerce'),
            'options' => [
                '' => __('Wybierz typ jednostek', 'woocommerce'),
                self::UNIT_TYPE_MASS => __('Masa (gramy)', 'woocommerce'),
                self::UNIT_TYPE_VOLUME => __('Objętość (mililitry)', 'woocommerce')
            ],
            'desc_tip' => true,
            'description' => __('Wybierz czy produkt jest mierzony w gramach czy mililitrach', 'woocommerce')
        ]);

        echo '</div>';
    }

    public static function saveVariationFields($variation_id, $loop): void
    {
        $unit_count = isset($_POST[self::META_KEY_UNIT_COUNT][$loop]) ? intval($_POST[self::META_KEY_UNIT_COUNT][$loop]) : '';
        $unit_type = isset($_POST[self::META_KEY_UNIT_TYPE][$loop]) ? sanitize_text_field($_POST[self::META_KEY_UNIT_TYPE][$loop]) : '';

        update_post_meta($variation_id, self::META_KEY_UNIT_COUNT, $unit_count);
        update_post_meta($variation_id, self::META_KEY_UNIT_TYPE, $unit_type);
    }

    public static function calculateUnitPrice($product_id, $price = null): string
    {
        $unit_count = get_post_meta($product_id, self::META_KEY_UNIT_COUNT, true);
        $unit_type = get_post_meta($product_id, self::META_KEY_UNIT_TYPE, true);

        if (empty($unit_count) || empty($unit_type) || $unit_count <= 0) {
            return '';
        }

        if ($price === null) {
            $product = wc_get_product($product_id);
            if (!$product) {
                return '';
            }
            $price = $product->get_price();
        }

        if (empty($price) || $price <= 0) {
            return '';
        }

        $unit_price = ($price / $unit_count) * 100;

        $unit_label = $unit_type === self::UNIT_TYPE_MASS ? '100g' : '100ml';

        return number_format($unit_price, 2, ',', '') . 'zł/' . $unit_label;
    }

    public static function getVariationUnitPrice($variation_id): string
    {
        $variation = wc_get_product($variation_id);
        if (!$variation) {
            return '';
        }

        return self::calculateUnitPrice($variation_id, $variation->get_price());
    }

    /**
     * @param WC_Product|array $product
     */
    public static function displayUnitPrice($product): string
    {
        if (!is_a($product, 'WC_Product')) {
            return '';
        }

        if ($product->is_type('variable')) {
            return '<div class="unit-price" style="display: none;"></div>';
        } else {
            $unit_price = self::calculateUnitPrice($product->get_id());

            if (empty($unit_price)) {
                return '';
            }

            return '<div class="unit-price">' . $unit_price . '</div>';
        }
    }
}
