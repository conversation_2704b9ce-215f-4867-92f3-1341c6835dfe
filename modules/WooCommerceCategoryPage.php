<?php

namespace DD\FitospaTheme;

class WooCommerceCategoryPage
{
    private const string SECOND_DESC_KEY = 'seconddesc';

    public static function init(): void
    {
        /** Show on adding category form */
        add_action( 'product_cat_add_form_fields', [self::class, 'addFieldOnAdding'], 10);

        /** Show on editing category form */
        add_action( 'product_cat_edit_form_fields', [self::class, 'addFieldOnEdit'], 10, 1);

        /** Save value on both forms */
        add_action( 'edit_term', [self::class, 'saveField'], 10, 3);
        add_action( 'created_term', [self::class, 'saveField'], 10, 3);

        /** Show value on frontend */
        add_action( 'woocommerce_after_shop_loop', [self::class, 'showValueOnFrontend'], 5);
        add_action( 'woocommerce_no_products_found', [self::class, 'showValueOnFrontend'], 15);

        /** Remove "Show X of Y products..." */
        remove_action( 'woocommerce_before_shop_loop', 'woocommerce_result_count', 20);
    }

    public static function addFieldOnAdding(): void
    {
        $settings = array(
            'textarea_name' => self::SECOND_DESC_KEY,
            'quicktags' => array( 'buttons' => 'em,strong,link' ),
            'tinymce' => array(
                'theme_advanced_buttons1' => 'bold,italic,strikethrough,separator,bullist,numlist,separator,blockquote,separator,justifyleft,justifycenter,justifyright,separator,link,unlink,separator,undo,redo,separator',
                'theme_advanced_buttons2' => '',
            ),
            'editor_css' => '<style>#wp-excerpt-editor-container .wp-editor-area{height:175px; width:100%;}</style>',
        );

        echo '<div class="form-field">
            <label for="seconddesc">Dolny opis</label>';

        wp_editor( '', self::SECOND_DESC_KEY, $settings );

       echo '<p class="description">SEO opis widoczny na dole strony</p></div>';
    }

    public static function addFieldOnEdit($term): void
    {
        $settings = array(
            'textarea_name' => self::SECOND_DESC_KEY,
            'quicktags' => array( 'buttons' => 'em,strong,link' ),
            'tinymce' => array(
                'theme_advanced_buttons1' => 'bold,italic,strikethrough,separator,bullist,numlist,separator,blockquote,separator,justifyleft,justifycenter,justifyright,separator,link,unlink,separator,undo,redo,separator',
                'theme_advanced_buttons2' => '',
            ),
            'editor_css' => '<style>#wp-excerpt-editor-container .wp-editor-area{height:175px; width:100%;}</style>',
        );

        $actualLowerDesc = htmlspecialchars_decode(get_term_meta($term->term_id, self::SECOND_DESC_KEY,true));

        echo '<tr class="form-field">
            <th scope="row" valign="top"><label for="second-desc">Dolny opis</label></th>
            <td>';

        wp_editor($actualLowerDesc, self::SECOND_DESC_KEY, $settings);

        echo '<p class="description">SEO opis widoczny na dole strony</p>
            </td>
        </tr>';
    }

    public static function saveField($term_id, $tt_id = '', $taxonomy = ''): void
    {
        if ( isset( $_POST[self::SECOND_DESC_KEY] ) && 'product_cat' === $taxonomy ) {
            update_term_meta( $term_id, self::SECOND_DESC_KEY, esc_attr($_POST[self::SECOND_DESC_KEY]));
        }
    }

    public static function showValueOnFrontend(): void
    {
        if (!is_product_taxonomy()) {
            return;
        }

        $term = get_queried_object();

        if (!$term) {
            return;
        }

        $value = get_term_meta($term->term_id, 'seconddesc', true);
        if (empty($value)) {
            return;
        }

        echo '<div class="term-description">' . wc_format_content(htmlspecialchars_decode($value)) . '</div>';
    }

    public static function displayMobileSubcategories(): void
    {
        if (!is_product_taxonomy()) {
            return;
        }

        $current_term = get_queried_object();

        if (!$current_term || !isset($current_term->term_id)) {
            return;
        }

        $subcategories = get_terms([
            'taxonomy' => 'product_cat',
            'parent' => $current_term->term_id,
            'hide_empty' => false,
            'orderby' => 'name',
            'order' => 'ASC'
        ]);

        if (empty($subcategories) || is_wp_error($subcategories)) {
            return;
        }

        echo '<div class="dd-mobile-subcategories mobile">';
        echo '<div class="dd-mobile-subcategories-wrapper">';
        echo '<div class="dd-mobile-subcategories-list" role="list" aria-label="Lista podkategorii">';

        foreach ($subcategories as $subcategory) {
            $category_link = get_term_link($subcategory);
            $category_name = esc_html($subcategory->name);

            if (!is_wp_error($category_link)) {
                echo '<a href="' . esc_url($category_link) . '" class="dd-subcategory-item" role="listitem" aria-label="Podkategoria: ' . $category_name . '">';
                echo '<span class="dd-subcategory-name">' . $category_name . '</span>';
                echo '</a>';
            }
        }

        echo '</div>';
        echo '</div>';
        echo '</div>';
    }
}