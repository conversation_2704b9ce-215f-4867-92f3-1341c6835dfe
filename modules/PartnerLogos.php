<?php

namespace DD\FitospaTheme;

class PartnerLogos
{
    private const OPTION_KEY = 'dd_partner_logos';
    private const CACHE_KEY = 'dd_partner_logos_cache';
    private const CACHE_EXPIRATION = 3600; // 1 hour

    public static function init(): void
    {
        add_action('admin_menu', [self::class, 'addAdminMenu']);
        add_action('admin_enqueue_scripts', [self::class, 'enqueueAdminAssets']);
        add_action('wp_ajax_dd_save_partners', [self::class, 'savePartners']);
        add_action('wp_ajax_dd_delete_partner', [self::class, 'deletePartner']);
        add_action('wp_ajax_dd_reorder_partners', [self::class, 'reorderPartners']);
        add_action('wp_ajax_dd_get_partner_edit_form', [self::class, 'getPartnerEditForm']);
    }

    public static function addAdminMenu(): void
    {
        // Do<PERSON>j podmenu "Zaufali nam" do istniejącego menu "Treści"
        add_submenu_page(
            'dd-content',
            'Zau<PERSON>li nam',
            'Zaufali nam',
            'manage_options',
            'dd-partner-logos',
            [self::class, 'renderAdminPage']
        );
    }

    public static function enqueueAdminAssets(): void
    {
        wp_enqueue_media();
        wp_enqueue_script('jquery-ui-sortable');
        
        wp_enqueue_script(
            'dd-admin-partners',
            get_template_directory_uri() . '/assets/js/admin-partners.js',
            ['jquery', 'jquery-ui-sortable'],
            DD_THEME_VERSION,
            true
        );

        wp_enqueue_style(
            'dd-admin-partners',
            get_template_directory_uri() . '/assets/css/admin-partners.css',
            [],
            DD_THEME_VERSION
        );

        wp_localize_script('dd-admin-partners', 'ddPartners', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dd_partners_nonce'),
            'confirmDelete' => 'Czy na pewno chcesz usunąć to logo partnera?',
        ]);
    }

    public static function renderAdminPage(): void
    {
        $partners = self::getPartners();
        ?>
        <div class="wrap dd-partners-admin">
            <h1>Zaufali nam - Logo partnerów</h1>
            
            <div class="dd-partners-container">
                <div class="dd-partners-header">
                    <div class="dd-partners-actions">
                        <button type="button" id="add-partner" class="button button-secondary">
                            <span class="dashicons dashicons-plus-alt"></span>
                            Dodaj nowe logo
                        </button>
                        <button type="button" id="save-partners" class="button button-primary">
                            Zapisz zmiany
                        </button>
                    </div>
                </div>

                <div class="dd-partners-table-container">
                    <table class="wp-list-table widefat fixed striped dd-partners-table">
                        <thead>
                            <tr>
                                <th class="column-order">Kolejność</th>
                                <th class="column-preview">Podgląd</th>
                                <th class="column-details">Szczegóły</th>
                                <th class="column-status">Status</th>
                                <th class="column-actions">Akcje</th>
                            </tr>
                        </thead>
                        <tbody id="partners-list">
                            <?php if (empty($partners)): ?>
                                <tr class="dd-empty-row">
                                    <td colspan="5" class="dd-empty-state">
                                        <div class="dd-empty-icon">
                                            <span class="dashicons dashicons-businessman"></span>
                                        </div>
                                        <h3>Brak logo partnerów</h3>
                                        <p>Kliknij "Dodaj nowe logo" aby rozpocząć dodawanie logo partnerów.</p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($partners as $index => $partner): ?>
                                    <?php self::renderPartnerRow($partner, $index); ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <script type="text/template" id="partner-template">
                <?php self::renderPartnerRow(self::getDefaultPartnerData(), '{{INDEX}}'); ?>
            </script>

            <div id="partner-edit-modal" class="dd-modal" style="display: none;">
                <div class="dd-modal-content">
                    <div class="dd-modal-header">
                        <h2>Edytuj logo partnera</h2>
                        <button type="button" class="dd-modal-close">&times;</button>
                    </div>
                    <div class="dd-modal-body">
                        <!-- Content will be loaded dynamically -->
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    private static function getDefaultPartnerData(): array
    {
        return [
            'image' => '',
            'alt' => '',
            'active' => true
        ];
    }

    public static function getPartners(): array
    {
        // Sprawdź cache
        $cached_partners = get_transient(self::CACHE_KEY);
        if ($cached_partners !== false) {
            return $cached_partners;
        }

        // Pobierz z bazy danych
        $partners = get_option(self::OPTION_KEY, []);

        // Zapisz w cache
        set_transient(self::CACHE_KEY, $partners, self::CACHE_EXPIRATION);
        
        return $partners;
    }

    private static function renderPartnerRow(array $partner, $index): void
    {
        $is_template = $index === '{{INDEX}}';
        $partner_number = $is_template ? '{{NUMBER}}' : ($index + 1);
        ?>
        <tr class="dd-partner-row" data-index="<?php echo esc_attr($index); ?>">
            <td class="column-order">
                <span class="dashicons dashicons-menu dd-drag-handle"></span>
                <span class="partner-number"><?php echo $partner_number; ?></span>
                <input type="hidden" name="partners[<?php echo esc_attr($index); ?>][image]"
                       value="<?php echo esc_attr($partner['image']); ?>" class="partner-image">
                <input type="hidden" name="partners[<?php echo esc_attr($index); ?>][alt]"
                       value="<?php echo esc_attr($partner['alt']); ?>" class="partner-alt">
                <input type="hidden" name="partners[<?php echo esc_attr($index); ?>][active]"
                       value="<?php echo $partner['active'] ? '1' : '0'; ?>" class="partner-active">
            </td>

            <td class="column-preview">
                <div class="dd-partner-preview">
                    <?php if (!empty($partner['image'])): ?>
                        <img src="<?php echo esc_url($partner['image']); ?>" alt="<?php echo esc_attr($partner['alt']); ?>" class="preview-logo">
                    <?php else: ?>
                        <div class="preview-placeholder">
                            <span class="dashicons dashicons-format-image"></span>
                            <span>Brak logo</span>
                        </div>
                    <?php endif; ?>
                </div>
            </td>

            <td class="column-details">
                <div class="partner-details">
                    <div class="partner-alt-text">
                        <strong>Alt:</strong> 
                        <span class="partner-alt-display"><?php echo esc_html($partner['alt'] ?: 'Brak tekstu alternatywnego'); ?></span>
                    </div>
                </div>
            </td>

            <td class="column-status">
                <div class="partner-status">
                    <?php if ($partner['active']): ?>
                        <span class="status-active">
                            <span class="dashicons dashicons-yes-alt"></span>
                            Aktywny
                        </span>
                    <?php else: ?>
                        <span class="status-inactive">
                            <span class="dashicons dashicons-dismiss"></span>
                            Nieaktywny
                        </span>
                    <?php endif; ?>
                </div>
            </td>

            <td class="column-actions">
                <div class="partner-actions">
                    <button type="button" class="button button-small dd-edit-partner" title="Edytuj">
                        <span class="dashicons dashicons-edit"></span>
                    </button>
                    <button type="button" class="button button-small dd-toggle-partner" 
                            title="<?php echo $partner['active'] ? 'Dezaktywuj' : 'Aktywuj'; ?>">
                        <span class="dashicons <?php echo $partner['active'] ? 'dashicons-hidden' : 'dashicons-visibility'; ?>"></span>
                    </button>
                    <button type="button" class="button button-small dd-delete-partner" title="Usuń">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </div>
            </td>
        </tr>
        <?php
    }

    public static function renderEditForm(array $partner, $index): void
    {
        ?>
        <form class="dd-partner-edit-form">
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label>Logo partnera <span class="required">*</span></label>
                    </th>
                    <td>
                        <div class="dd-image-upload">
                            <input type="hidden" name="image" value="<?php echo esc_attr($partner['image']); ?>" class="image-url">
                            <div class="image-preview">
                                <?php if (!empty($partner['image'])): ?>
                                    <img src="<?php echo esc_url($partner['image']); ?>" alt="Preview">
                                <?php endif; ?>
                            </div>
                            <div class="image-buttons">
                                <button type="button" class="button upload-image">Wybierz logo</button>
                                <button type="button" class="button remove-image" style="<?php echo empty($partner['image']) ? 'display:none;' : ''; ?>">Usuń</button>
                            </div>
                        </div>
                        <p class="description">Wybierz logo partnera. Zalecany format: PNG lub SVG z przezroczystym tłem.</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label>Tekst alternatywny <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="text" name="alt" value="<?php echo esc_attr($partner['alt']); ?>" 
                               class="regular-text" required 
                               placeholder="np. Logo firmy ABC">
                        <p class="description">Tekst alternatywny dla logo (ważny dla SEO i dostępności).</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label>Status</label>
                    </th>
                    <td>
                        <label>
                            <input type="checkbox" name="active" value="1" <?php checked($partner['active'] ?? true); ?>>
                            Logo aktywne (wyświetlane na stronie)
                        </label>
                    </td>
                </tr>
            </table>

            <div class="dd-modal-actions">
                <button type="button" class="button button-primary dd-save-partner">Zapisz zmiany</button>
                <button type="button" class="button dd-modal-close">Anuluj</button>
            </div>
        </form>
        <?php
    }

    public static function savePartners(): void
    {
        check_ajax_referer('dd_partners_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Brak uprawnień');
        }

        $partners = $_POST['partners'] ?? [];
        $sanitized_partners = [];

        foreach ($partners as $partner) {
            $sanitized_partner = [
                'image' => esc_url_raw($partner['image'] ?? ''),
                'alt' => sanitize_text_field($partner['alt'] ?? ''),
                'active' => !empty($partner['active'])
            ];

            // Sprawdź czy wymagane pola są wypełnione
            if (!empty($sanitized_partner['image']) && !empty($sanitized_partner['alt'])) {
                $sanitized_partners[] = $sanitized_partner;
            }
        }

        update_option(self::OPTION_KEY, $sanitized_partners);
        delete_transient(self::CACHE_KEY);

        wp_send_json_success(['message' => 'Logo partnerów zostały zapisane']);
    }

    public static function deletePartner(): void
    {
        check_ajax_referer('dd_partners_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Brak uprawnień');
        }

        $index = intval($_POST['index'] ?? -1);
        $partners = self::getPartners();

        if (isset($partners[$index])) {
            unset($partners[$index]);
            $partners = array_values($partners); // Reindex array

            update_option(self::OPTION_KEY, $partners);
            delete_transient(self::CACHE_KEY);

            wp_send_json_success(['message' => 'Logo partnera zostało usunięte']);
        }

        wp_send_json_error(['message' => 'Nie można usunąć logo partnera']);
    }

    public static function reorderPartners(): void
    {
        check_ajax_referer('dd_partners_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Brak uprawnień');
        }

        $order = $_POST['order'] ?? [];
        $partners = self::getPartners();
        $reordered_partners = [];

        foreach ($order as $index) {
            $index = intval($index);
            if (isset($partners[$index])) {
                $reordered_partners[] = $partners[$index];
            }
        }

        if (!empty($reordered_partners)) {
            update_option(self::OPTION_KEY, $reordered_partners);
            delete_transient(self::CACHE_KEY);
            wp_send_json_success(['message' => 'Kolejność logo partnerów została zmieniona']);
        }

        wp_send_json_error(['message' => 'Nie można zmienić kolejności']);
    }

    /**
     * @return array<array{image: string, alt: string, active: bool}>
     */
    public static function getActivePartners(): array
    {
        $all_partners = self::getPartners();
        return array_filter($all_partners, function($partner) {
            return !empty($partner['active']);
        });
    }

    public static function getPartnerEditForm(): void
    {
        check_ajax_referer('dd_partners_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Brak uprawnień');
        }

        $partner = $_POST['partner'] ?? [];
        $index = intval($_POST['index'] ?? 0);

        // Sanitize partner data
        $partner = [
            'image' => esc_url_raw($partner['image'] ?? ''),
            'alt' => sanitize_text_field($partner['alt'] ?? ''),
            'active' => !empty($partner['active'])
        ];

        ob_start();
        self::renderEditForm($partner, $index);
        $html = ob_get_clean();

        wp_send_json_success(['html' => $html]);
    }
}
