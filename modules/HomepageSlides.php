<?php

namespace DD\FitospaTheme;

class HomepageSlides
{
    private const OPTION_KEY = 'dd_homepage_slides';
    private const CACHE_KEY = 'dd_homepage_slides_cache';
    private const CACHE_EXPIRATION = 3600; // 1 hour

    public static function init(): void
    {
        add_action('admin_menu', [self::class, 'addAdminMenu']);
        add_action('admin_enqueue_scripts', [self::class, 'enqueueAdminAssets']);
        add_action('wp_ajax_dd_save_slides', [self::class, 'saveSlides']);
        add_action('wp_ajax_dd_delete_slide', [self::class, 'deleteSlide']);
        add_action('wp_ajax_dd_reorder_slides', [self::class, 'reorderSlides']);
        add_action('wp_ajax_dd_get_slide_edit_form', [self::class, 'getSlideEditForm']);
    }

    public static function addAdminMenu(): void
    {
        // Dodaj główne menu "Treści"
        add_menu_page(
            'Treści',
            'Treści',
            'manage_options',
            'dd-content',
            static fn() => '',
            'dashicons-edit-page',
            25
        );

        // Dodaj podmenu "Bannery na stronie głównej"
        add_submenu_page(
            'dd-content',
            'Bannery na stronie głównej',
            'Bannery na stronie głównej',
            'manage_options',
            'dd-homepage-slides',
            [self::class, 'renderAdminPage']
        );
    }

    public static function enqueueAdminAssets(): void
    {
        wp_enqueue_media();
        wp_enqueue_script('jquery-ui-sortable');
        
        wp_enqueue_script(
            'dd-admin-slides',
            get_template_directory_uri() . '/assets/js/admin-slides.js',
            ['jquery', 'jquery-ui-sortable'],
            DD_THEME_VERSION,
            true
        );

        wp_enqueue_style(
            'dd-admin-slides',
            get_template_directory_uri() . '/assets/css/admin-slides.css',
            [],
            DD_THEME_VERSION
        );

        wp_localize_script('dd-admin-slides', 'ddSlides', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dd_slides_nonce'),
            'confirmDelete' => 'Czy na pewno chcesz usunąć ten slajd?',
        ]);
    }

    public static function renderAdminPage(): void
    {
        $slides = self::getSlides();
        ?>
        <div class="wrap dd-slides-admin">
            <h1>Bannery na stronie głównej</h1>

            <div class="dd-slides-container">
                <div class="dd-slides-header">
                    <button type="button" class="button button-primary" id="add-slide">
                        <span class="dashicons dashicons-plus-alt"></span>
                        Dodaj nowy slajd
                    </button>
                    <button type="button" class="button button-primary" id="save-slides">
                        <span class="dashicons dashicons-yes"></span>
                        Zapisz wszystkie slajdy
                    </button>
                </div>

                <div class="dd-slides-table-container">
                    <table class="wp-list-table widefat fixed striped dd-slides-table">
                        <thead>
                            <tr>
                                <th class="column-order">Kolejność</th>
                                <th class="column-preview">Podgląd</th>
                                <th class="column-details">Szczegóły</th>
                                <th class="column-status">Status</th>
                                <th class="column-actions">Akcje</th>
                            </tr>
                        </thead>
                        <tbody id="slides-list">
                            <?php if (empty($slides)): ?>
                                <tr class="dd-empty-row">
                                    <td colspan="5" class="dd-empty-state">
                                        <div class="dd-empty-icon">
                                            <span class="dashicons dashicons-images-alt2"></span>
                                        </div>
                                        <h3>Brak slajdów</h3>
                                        <p>Kliknij "Dodaj nowy slajd" aby rozpocząć tworzenie bannerów na stronie głównej.</p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($slides as $index => $slide): ?>
                                    <?php self::renderSlideRow($slide, $index); ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <script type="text/template" id="slide-template">
                <?php self::renderSlideRow(self::getDefaultSlideData(), '{{INDEX}}'); ?>
            </script>

            <div id="slide-edit-modal" class="dd-modal" style="display: none;">
                <div class="dd-modal-content">
                    <div class="dd-modal-header">
                        <h2>Edytuj slajd</h2>
                        <button type="button" class="dd-modal-close">&times;</button>
                    </div>
                    <div class="dd-modal-body">
                        <!-- Content will be loaded dynamically -->
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    private static function getDefaultSlideData(): array
    {
        return [
            'image' => '',
            'mobile_image' => '',
            'alt' => '',
            'link' => '',
            'active' => true
        ];
    }

    public static function getSlides(): array
    {
        // Sprawdź cache
        $cached_slides = get_transient(self::CACHE_KEY);
        if ($cached_slides !== false) {
            return $cached_slides;
        }

        // Pobierz z bazy danych
        $slides = get_option(self::OPTION_KEY, []);

        // Zapisz w cache
        set_transient(self::CACHE_KEY, $slides, self::CACHE_EXPIRATION);
        
        return $slides;
    }

    private static function renderSlideRow(array $slide, $index): void
    {
        $is_template = $index === '{{INDEX}}';
        $slide_number = $is_template ? '{{NUMBER}}' : ($index + 1);
        ?>
        <tr class="dd-slide-row" data-index="<?php echo esc_attr($index); ?>">
            <td class="column-order">
                <span class="dashicons dashicons-menu dd-drag-handle"></span>
                <span class="slide-number"><?php echo $slide_number; ?></span>
                <input type="hidden" name="slides[<?php echo esc_attr($index); ?>][image]"
                       value="<?php echo esc_attr($slide['image']); ?>" class="slide-image">
                <input type="hidden" name="slides[<?php echo esc_attr($index); ?>][mobile_image]"
                       value="<?php echo esc_attr($slide['mobile_image']); ?>" class="slide-mobile-image">
                <input type="hidden" name="slides[<?php echo esc_attr($index); ?>][alt]"
                       value="<?php echo esc_attr($slide['alt']); ?>" class="slide-alt">
                <input type="hidden" name="slides[<?php echo esc_attr($index); ?>][link]"
                       value="<?php echo esc_attr($slide['link']); ?>" class="slide-link">
                <input type="hidden" name="slides[<?php echo esc_attr($index); ?>][active]"
                       value="<?php echo $slide['active'] ? '1' : '0'; ?>" class="slide-active">
            </td>

            <td class="column-preview">
                <div class="dd-slide-preview">
                    <?php if (!empty($slide['image'])): ?>
                        <img src="<?php echo esc_url($slide['image']); ?>" alt="<?php echo esc_attr($slide['alt']); ?>" class="preview-desktop">
                    <?php else: ?>
                        <div class="preview-placeholder">
                            <span class="dashicons dashicons-format-image"></span>
                            <span>Brak zdjęcia</span>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($slide['mobile_image'])): ?>
                        <img src="<?php echo esc_url($slide['mobile_image']); ?>" alt="<?php echo esc_attr($slide['alt']); ?>" class="preview-mobile">
                        <span class="mobile-indicator">Mobile</span>
                    <?php endif; ?>
                </div>
            </td>

            <td class="column-details">
                <div class="slide-details">
                    <strong class="slide-alt"><?php echo esc_html($slide['alt'] ?: 'Brak tekstu alternatywnego'); ?></strong>
                    <div class="slide-link-display">
                        <span class="dashicons dashicons-admin-links"></span>
                        <code><?php echo esc_html($slide['link'] ?: 'Brak linku'); ?></code>
                    </div>
                </div>
            </td>

            <td class="column-status">
                <div class="slide-status">
                    <?php if ($slide['active']): ?>
                        <span class="status-active">
                            <span class="dashicons dashicons-yes-alt"></span>
                            Aktywny
                        </span>
                    <?php else: ?>
                        <span class="status-inactive">
                            <span class="dashicons dashicons-dismiss"></span>
                            Nieaktywny
                        </span>
                    <?php endif; ?>
                </div>
            </td>

            <td class="column-actions">
                <div class="slide-actions">
                    <button type="button" class="button button-small dd-edit-slide" title="Edytuj slajd">
                        <span class="dashicons dashicons-edit"></span>
                    </button>
                    <button type="button" class="button button-small dd-toggle-slide" title="<?php echo $slide['active'] ? 'Dezaktywuj' : 'Aktywuj'; ?>">
                        <span class="dashicons dashicons-<?php echo $slide['active'] ? 'hidden' : 'visibility'; ?>"></span>
                    </button>
                    <button type="button" class="button button-small dd-delete-slide" title="Usuń slajd">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </div>
            </td>
        </tr>
        <?php
    }

    public static function renderEditForm(array $slide, $index): void
    {
        ?>
        <form class="dd-slide-edit-form">
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label>Zdjęcie desktop <span class="required">*</span></label>
                    </th>
                    <td>
                        <div class="dd-image-upload">
                            <input type="hidden" name="image" value="<?php echo esc_attr($slide['image']); ?>" class="image-url">
                            <div class="image-preview">
                                <?php if (!empty($slide['image'])): ?>
                                    <img src="<?php echo esc_url($slide['image']); ?>" alt="Preview">
                                <?php endif; ?>
                            </div>
                            <div class="image-buttons">
                                <button type="button" class="button upload-image">Wybierz zdjęcie</button>
                                <button type="button" class="button remove-image" style="<?php echo empty($slide['image']) ? 'display:none;' : ''; ?>">Usuń</button>
                            </div>
                        </div>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label>Zdjęcie mobile <span class="required">*</span></label>
                    </th>
                    <td>
                        <div class="dd-image-upload">
                            <input type="hidden" name="mobile_image" value="<?php echo esc_attr($slide['mobile_image']); ?>" class="image-url" required>
                            <div class="image-preview">
                                <?php if (!empty($slide['mobile_image'])): ?>
                                    <img src="<?php echo esc_url($slide['mobile_image']); ?>" alt="Preview">
                                <?php endif; ?>
                            </div>
                            <div class="image-buttons">
                                <button type="button" class="button upload-image">Wybierz zdjęcie</button>
                                <button type="button" class="button remove-image" style="<?php echo empty($slide['mobile_image']) ? 'display:none;' : ''; ?>">Usuń</button>
                            </div>
                            <p class="description">Jeśli nie wybierzesz, zostanie użyte zdjęcie desktop</p>
                        </div>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label>Tekst alternatywny <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="text" name="alt" value="<?php echo esc_attr($slide['alt']); ?>" class="regular-text" required>
                        <p class="description">Opis zdjęcia dla wyszukiwarek i czytników ekranu</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label>Link docelowy <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="text" name="link" value="<?php echo esc_attr($slide['link']); ?>" class="regular-text" required placeholder="np. sklep lub produkty/kategoria">
                        <p class="description">Relatywny link bez domeny (np. "sklep" lub "produkty/kategoria")</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label>Status</label>
                    </th>
                    <td>
                        <label>
                            <input type="checkbox" name="active" value="1" <?php checked($slide['active'] ?? true); ?>>
                            Slajd aktywny (wyświetlany na stronie)
                        </label>
                    </td>
                </tr>
            </table>

            <div class="dd-modal-actions">
                <button type="button" class="button button-primary dd-save-slide">Zapisz zmiany</button>
                <button type="button" class="button dd-modal-close">Anuluj</button>
            </div>
        </form>
        <?php
    }

    public static function saveSlides(): void
    {
        check_ajax_referer('dd_slides_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Brak uprawnień');
        }

        $slides = isset($_POST['slides']) ? $_POST['slides'] : [];
        $sanitized_slides = [];

        foreach ($slides as $slide) {
            $sanitized_slide = [
                'image' => esc_url_raw($slide['image'] ?? ''),
                'mobile_image' => esc_url_raw($slide['mobile_image'] ?? ''),
                'alt' => sanitize_text_field($slide['alt'] ?? ''),
                'link' => sanitize_text_field($slide['link'] ?? ''),
                'active' => !empty($slide['active'])
            ];

            if (empty($sanitized_slide['image']) || empty($sanitized_slide['mobile_image']) || empty($sanitized_slide['alt']) || empty($sanitized_slide['link'])) {
                wp_send_json_error(['message' => 'Wypełnij wszystkie wymagane pola']);
            }

            $sanitized_slides[] = $sanitized_slide;
        }

        update_option(self::OPTION_KEY, $sanitized_slides);
        delete_transient(self::CACHE_KEY);

        wp_send_json_success(['message' => 'Slajdy zostały zapisane']);
    }

    public static function deleteSlide(): void
    {
        check_ajax_referer('dd_slides_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Brak uprawnień');
        }

        $index = intval($_POST['index'] ?? -1);
        $slides = self::getSlides();

        if (isset($slides[$index])) {
            unset($slides[$index]);
            $slides = array_values($slides); // Reindex array

            update_option(self::OPTION_KEY, $slides);
            delete_transient(self::CACHE_KEY);

            wp_send_json_success(['message' => 'Slajd został usunięty']);
        }

        wp_send_json_error(['message' => 'Nie można usunąć slajdu']);
    }

    public static function reorderSlides(): void
    {
        check_ajax_referer('dd_slides_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Brak uprawnień');
        }

        $order = $_POST['order'] ?? [];
        $slides = self::getSlides();
        $reordered_slides = [];

        foreach ($order as $index) {
            $index = intval($index);
            if (isset($slides[$index])) {
                $reordered_slides[] = $slides[$index];
            }
        }

        if (!empty($reordered_slides)) {
            update_option(self::OPTION_KEY, $reordered_slides);
            delete_transient(self::CACHE_KEY);
            wp_send_json_success(['message' => 'Kolejność slajdów została zmieniona']);
        }

        wp_send_json_error(['message' => 'Nie można zmienić kolejności']);
    }

    /**
     * @return array<array{image: string, mobile_image: string, alt: string, link: string, active: bool}>
     */
    public static function getActiveSlides(): array
    {
        $all_slides = self::getSlides();
        return array_filter($all_slides, function($slide) {
            return !empty($slide['active']);
        });
    }

    public static function getSlideEditForm(): void
    {
        check_ajax_referer('dd_slides_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Brak uprawnień');
        }

        $slide = $_POST['slide'] ?? [];
        $index = intval($_POST['index'] ?? 0);

        // Sanitize slide data
        $slide = [
            'image' => esc_url_raw($slide['image'] ?? ''),
            'mobile_image' => esc_url_raw($slide['mobile_image'] ?? ''),
            'alt' => sanitize_text_field($slide['alt'] ?? ''),
            'link' => sanitize_text_field($slide['link'] ?? ''),
            'active' => !empty($slide['active'])
        ];

        ob_start();
        self::renderEditForm($slide, $index);
        $html = ob_get_clean();

        wp_send_json_success(['html' => $html]);
    }
}
