<?php

namespace DD\FitospaTheme;

use WP_Query;

class WooCommerceProductListing
{
    public static function init(): void
    {
        add_shortcode('dd_products', function ($atts = [], $content = null, $tag = '') {
            $atts = array_change_key_case( (array) $atts, CASE_LOWER );

            $dd_atts = shortcode_atts(
                [
                    'ids' => '',
                    'is_swiper' => 'false',
                ], $atts, $tag
            );

            $args = [
                'posts_per_page' => 4,
                'post_type'      => 'product',
                'post__in'       => !empty($dd_atts['ids']) ? explode(',', $dd_atts['ids']) : [],
                'is_swiper'      => filter_var($dd_atts['is_swiper'], FILTER_VALIDATE_BOOLEAN),
                'section_class'  => !empty($atts['section_class']) ? $atts['section_class'] : 'swiper-products',
            ];

            return self::generateListing($args);
        });
    }

    public static function generateListing(array $args): string
    {
        $args = wp_parse_args($args, [
            'post_type'     => 'product',
            'status'        => 'publish',
            'is_swiper'     => false,
            'section_class' => 'swiper-products',
        ]);

        $isSwiper = $args['is_swiper'];
        $html = '';

        if (!$isSwiper) {
            $html .= '<div class="woocommerce"><ul class="products columns-4">';
        }

        $query = new WP_Query($args);

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                ob_start();
                wc_get_template_part('content', 'product');
                $html .= $isSwiper 
                    ? '<div class="swiper-slide">' . ob_get_clean() . '</div>'
                    : '<div class="product-item">' . ob_get_clean() . '</div>';
            }
            wp_reset_postdata();
        } else {
            $html .= '<p>Nie znaleziono produktów</p>';
        }

        if (!$isSwiper) {
            $html .= '</ul></div>';
        }

        return $html;
    }
}