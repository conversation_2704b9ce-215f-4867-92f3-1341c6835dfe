<?php

namespace DD\FitospaTheme;

class WooCommerceProductTabs
{
    private const META_KEY_SKLAD = '_sklad_produktu';
    private const META_KEY_DZIALANIE = '_dzialanie_produktu';
    private const META_KEY_DLA_KOGO = '_dla_kogo_produkt';
    private const META_KEY_JAK_STOSOWAC = '_jak_stosowac_produkt';

    public static function init(): void
    {
        // Dodaj nową zakładkę "Treść" w panelu edycji produktu
        add_filter('woocommerce_product_data_tabs', [self::class, 'addProductDataTab']);
        add_action('woocommerce_product_data_panels', [self::class, 'addProductDataPanel']);

        // Zapisz wartości pól
        add_action('woocommerce_process_product_meta', [self::class, 'saveProductFields']);

        // <PERSON><PERSON><PERSON> zakładki na stronie produktu
        add_filter('woocommerce_product_tabs', [self::class, 'addProductTabs']);
    }

    public static function addProductDataTab($tabs): array
    {
        $tabs['dd_product_content'] = [
            'label' => __('Treść', 'woocommerce'),
            'target' => 'dd_product_content_data',
            'class' => ['show_if_simple', 'show_if_variable'],
            'priority' => 25
        ];

        return $tabs;
    }

    public static function addProductDataPanel(): void
    {
        echo '<div id="dd_product_content_data" class="panel woocommerce_options_panel">';
        self::addProductFields();
        echo '</div>';
    }

    public static function addProductFields(): void
    {
        global $post;

        // Dodaj style dla poprawnego wyświetlania etykiet i odstępów
        echo '<style>
            #dd_product_content_data .form-field {
                padding: 15px 20px;
                margin-bottom: 30px;
                border-bottom: 1px solid #eee;
            }
            #dd_product_content_data .form-field:last-child {
                border-bottom: none;
            }
            #dd_product_content_data .form-field h3 {
                margin-top: 0;
                margin-bottom: 15px;
                font-size: 16px;
                font-weight: 600;
                color: #23282d;
            }
            #dd_product_content_data .form-field label {
                display: block;
                font-weight: 600;
                margin-bottom: 10px;
                font-size: 14px;
            }
            #dd_product_content_data .wp-editor-wrap {
                margin-top: 10px;
            }
        </style>';

        // Skład
        echo '<div class="form-field">';
        echo '<h3>' . __('Skład', 'woocommerce') . '</h3>';
        $sklad_content = get_post_meta($post->ID, self::META_KEY_SKLAD, true);
        wp_editor($sklad_content, self::META_KEY_SKLAD, [
            'media_buttons' => true,
            'textarea_name' => self::META_KEY_SKLAD,
            'textarea_rows' => 10,
            'editor_class' => 'widefat'
        ]);
        echo '</div>';

        // Działanie
        echo '<div class="form-field">';
        echo '<h3>' . __('Działanie', 'woocommerce') . '</h3>';
        $dzialanie_content = get_post_meta($post->ID, self::META_KEY_DZIALANIE, true);
        wp_editor($dzialanie_content, self::META_KEY_DZIALANIE, [
            'media_buttons' => true,
            'textarea_name' => self::META_KEY_DZIALANIE,
            'textarea_rows' => 10,
            'editor_class' => 'widefat'
        ]);
        echo '</div>';

        // Dla kogo
        echo '<div class="form-field">';
        echo '<h3>' . __('Dla kogo', 'woocommerce') . '</h3>';
        $dla_kogo_content = get_post_meta($post->ID, self::META_KEY_DLA_KOGO, true);
        wp_editor($dla_kogo_content, self::META_KEY_DLA_KOGO, [
            'media_buttons' => true,
            'textarea_name' => self::META_KEY_DLA_KOGO,
            'textarea_rows' => 10,
            'editor_class' => 'widefat'
        ]);
        echo '</div>';

        // Jak stosować
        echo '<div class="form-field">';
        echo '<h3>' . __('Jak stosować', 'woocommerce') . '</h3>';
        $jak_stosowac_content = get_post_meta($post->ID, self::META_KEY_JAK_STOSOWAC, true);
        wp_editor($jak_stosowac_content, self::META_KEY_JAK_STOSOWAC, [
            'media_buttons' => true,
            'textarea_name' => self::META_KEY_JAK_STOSOWAC,
            'textarea_rows' => 10,
            'editor_class' => 'widefat'
        ]);
        echo '</div>';
    }

    public static function saveProductFields($post_id): void
    {
        if (isset($_POST[self::META_KEY_SKLAD])) {
            update_post_meta($post_id, self::META_KEY_SKLAD, wp_kses_post($_POST[self::META_KEY_SKLAD]));
        }

        if (isset($_POST[self::META_KEY_DZIALANIE])) {
            update_post_meta($post_id, self::META_KEY_DZIALANIE, wp_kses_post($_POST[self::META_KEY_DZIALANIE]));
        }

        if (isset($_POST[self::META_KEY_DLA_KOGO])) {
            update_post_meta($post_id, self::META_KEY_DLA_KOGO, wp_kses_post($_POST[self::META_KEY_DLA_KOGO]));
        }

        if (isset($_POST[self::META_KEY_JAK_STOSOWAC])) {
            update_post_meta($post_id, self::META_KEY_JAK_STOSOWAC, wp_kses_post($_POST[self::META_KEY_JAK_STOSOWAC]));
        }
    }

    public static function addProductTabs($tabs): array
    {
        global $post;

        $dzialanie = get_post_meta($post->ID, self::META_KEY_DZIALANIE, true);
        if (!empty($dzialanie)) {
            $tabs['dzialanie'] = [
                'title' => __('Działanie', 'woocommerce'),
                'priority' => 30,
                'callback' => [self::class, 'dzialanieTabContent']
            ];
        }

        $dla_kogo = get_post_meta($post->ID, self::META_KEY_DLA_KOGO, true);
        if (!empty($dla_kogo)) {
            $tabs['dla_kogo'] = [
                'title' => __('Dla kogo', 'woocommerce'),
                'priority' => 40,
                'callback' => [self::class, 'dlaKogoTabContent']
            ];
        }

        $jak_stosowac = get_post_meta($post->ID, self::META_KEY_JAK_STOSOWAC, true);
        if (!empty($jak_stosowac)) {
            $tabs['jak_stosowac'] = [
                'title' => __('Jak stosować', 'woocommerce'),
                'priority' => 50,
                'callback' => [self::class, 'jakStosowacTabContent']
            ];
        }

        $sklad = get_post_meta($post->ID, self::META_KEY_SKLAD, true);
        if (!empty($sklad)) {
            $tabs['sklad'] = [
                'title' => __('Skład', 'woocommerce'),
                'priority' => 60,
                'callback' => [self::class, 'skladTabContent']
            ];
        }

        return $tabs;
    }

    public static function skladTabContent(): void
    {
        global $post;
        $sklad = get_post_meta($post->ID, self::META_KEY_SKLAD, true);
        if (!empty($sklad)) {
            echo '<div class="product-tab-content sklad-content">';
            echo apply_filters('the_content', $sklad);
            echo '</div>';
        }
    }

    public static function dzialanieTabContent(): void
    {
        global $post;
        $dzialanie = get_post_meta($post->ID, self::META_KEY_DZIALANIE, true);
        if (!empty($dzialanie)) {
            echo '<div class="product-tab-content dzialanie-content">';
            echo apply_filters('the_content', $dzialanie);
            echo '</div>';
        }
    }

    public static function dlaKogoTabContent(): void
    {
        global $post;
        $dla_kogo = get_post_meta($post->ID, self::META_KEY_DLA_KOGO, true);
        if (!empty($dla_kogo)) {
            echo '<div class="product-tab-content dla-kogo-content">';
            echo apply_filters('the_content', $dla_kogo);
            echo '</div>';
        }
    }

    public static function jakStosowacTabContent(): void
    {
        global $post;
        $jak_stosowac = get_post_meta($post->ID, self::META_KEY_JAK_STOSOWAC, true);
        if (!empty($jak_stosowac)) {
            echo '<div class="product-tab-content jak-stosowac-content">';
            echo apply_filters('the_content', $jak_stosowac);
            echo '</div>';
        }
    }


}
