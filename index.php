
<?php
// Załaduj nagłówek
get_header(); ?>

<?php
// <PERSON>pra<PERSON><PERSON><PERSON>, czy jest to strona główna
if ( is_front_page() ) :
    // Strona główna
    ?>
    <section class="homepage-content">
        <!-- Ręcznie kodowana treść strony głównej -->
    </section>

<?php
// Sprawdza, czy jest to strona lub wpis
elseif ( is_page() || is_single() ) :
    // Strona lub wpis
    while ( have_posts() ) : the_post(); ?>
        <article class="page-content dd-container">
            <?php dd_generate_breadcrumbs() ?>
            <?php the_content(); // Wyświetla treść z edytora WordPress ?>
        </article>
    <?php endwhile;

else : ?>
    <!-- Inne strony lub wpisy -->
    <section class="default-content dd-container">
        <?php
        while ( have_posts() ) : the_post();
            the_content(); // Wyświetla treść z edytora
        endwhile;
        ?>
    </section>
<?php endif; ?>

<?php
// Załaduj stopkę
get_footer();
