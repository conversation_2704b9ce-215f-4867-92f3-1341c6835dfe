<?php

/**
 * @version 1.6.4
 */

get_header();

global $product;
if (!$product || !is_a($product, 'WC_Product')) {
    $product_id = get_the_ID();
    $product = wc_get_product($product_id);
}

require_once plugin_dir_path(__FILE__) . 'product-variations.php';
?>

<main class="dd-container">

    <?php defined('ABSPATH') || exit; ?>

    <div id="product-<?php the_ID(); ?>" <?php wc_product_class('', $product); ?>>
        <?php dd_generate_breadcrumbs(); ?>

        <?php
        /**
         * Hook: woocommerce_before_main_content.
         *
         * @hooked woocommerce_output_content_wrapper - 10
         * @hooked woocommerce_breadcrumb - 20
         */
        do_action('woocommerce_before_main_content');
        ?>

        <div class="single-product-wrapper">

            <div class="product-gallery">
                <div class="product-thumbnails">
                    <?php
                    $attachment_ids = $product->get_gallery_image_ids();
                    $featured_image_id = $product->get_image_id();

                    $is_variants = $product->is_type('variable');
                    if ($is_variants) {
                        $available_variations = $product->get_available_variations();
                        $first_variation = reset($available_variations);
                        $featured_image_url = wp_get_attachment_image_url($first_variation['image_id'], 'full');
                        $variation_description = wc_get_product($first_variation['variation_id'])->get_description();
                    } else {
                        $featured_image_url = wp_get_attachment_image_url($featured_image_id, 'full');
                        $variation_description = '';
                    }

                    $thumbnail_url = wp_get_attachment_image_url($featured_image_id, 'thumbnail');
                    echo '<img src="' . esc_url($thumbnail_url) . '" alt="" class="thumbnail dd-thumbnail-active" data-fullsize="' . esc_url(wp_get_attachment_image_url($featured_image_id, 'full')) . '" />';

                    if ($attachment_ids) {
                        foreach ($attachment_ids as $attachment_id) {
                            $thumbnail_url = wp_get_attachment_image_url($attachment_id, 'thumbnail');
                            if (!is_string($thumbnail_url) || $thumbnail_url === '') {
                                continue;
                            }
                            echo '<img src="' . esc_url($thumbnail_url) . '" alt="" class="thumbnail" data-fullsize="' . esc_url(wp_get_attachment_image_url($attachment_id, 'full')) . '" />';
                        }
                    }
                    ?>
                </div>

                <div class="product-images">
                    <?php
                    echo '<img src="' . esc_url($featured_image_url) . '" alt="" id="main-product-image" />';
                    ?>
                </div>
            </div>

            <div class="product-details">
                <?php
                woocommerce_template_single_title();

                if (!empty($variation_description)): ?>
                    <div class="woocommerce-product-details__short-description">
                        <p class="variation-description"><?php echo esc_html($variation_description); ?></p>
                    </div>
                <?php else: ?>
                    <?php woocommerce_template_single_excerpt(); ?>
                <?php endif; ?>

                <?php
                dd_single_product_variations($product);

                dd_display_product_price($product);
                dd_display_stock_availability($product);
                dd_display_add_to_cart_button($product);

                dd_display_unit_price($product);
                dd_display_product_omnibus_message($product);
                ?>
            </div>

            <!-- Tagi -->
            <?php
            $current_tags = get_the_terms(get_the_ID(), 'product_tag');
            if ($current_tags && !is_wp_error($current_tags)) {
                echo '<div class="dd_product_tags">';
                foreach ($current_tags as $tag) {
                    $tag_title = $tag->name;
                    $tag_link = get_term_link($tag);
                    echo '<a href="' . $tag_link . '">#' . $tag_title . '</a>';
                }
                echo '</div>';
            }
            ?>
        </div>
    </div>

    <?php $product_tabs = apply_filters('woocommerce_product_tabs', array()); ?>

    <div class="product-accordion mobile">
        <?php
            // Wyświetl wszystkie zakładki z WooCommerce
            $first_tab = true;
            foreach ($product_tabs as $key => $product_tab) :
                $active_class = $first_tab ? ' one' : '';
                $icon_class = $first_tab ? 'fa-chevron-up' : 'fa-chevron-down';
                $content_class = $first_tab ? ' active' : '';

                echo '<h2 class="accordion-toggle' . $active_class . '" id="' . esc_attr($key) . '">' . esc_html($product_tab['title']) . ' <span class="accordion-icon"><i class="fa-solid ' . $icon_class . '"></i></span></h2>';
                echo '<div class="accordion-content dd-product-full-description' . $content_class . '">';
                if (isset($product_tab['callback'])) {
                    call_user_func($product_tab['callback'], $key, $product_tab);
                }
                echo '</div>';

                $first_tab = false;
            endforeach;
        ?>
    </div>

    <div class="dd-product-tabs desktop">
        <div class="tabs">
            <?php $first_tab_desktop = true; ?>
            <?php foreach ( $product_tabs as $key => $product_tab ) : ?>
                <div class="tab <?php echo esc_attr( $key ); ?>_tab <?php if ($first_tab_desktop) echo 'active'; ?>" data-tab="tab-<?php echo esc_attr( $key ); ?>"><?php echo wp_kses_post( apply_filters( 'woocommerce_product_' . $key . '_tab_title', $product_tab['title'], $key ) ); ?></div>
                <?php $first_tab_desktop = false; ?>
            <?php endforeach; ?>
        </div>

        <div class="dd-product-full-description">
            <?php foreach ( $product_tabs as $key => $product_tab ) : ?>
                <div class="tab-panel" data-tab="tab-<?php echo esc_attr( $key ); ?>">
                    <?php
                    if ( isset( $product_tab['callback'] ) ) {
                        call_user_func( $product_tab['callback'], $key, $product_tab );
                    }
                    ?>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <div class="dd-products-opinions">
        <?php echo do_shortcode('[trustindex no-registration=google]'); ?>
    </div>

    <?php
    /**
     * Hook: woocommerce_after_main_content.
     *
     * @hooked woocommerce_output_content_wrapper_end - 10
     * @hooked woocommerce_output_sidebar - 20
     */
    do_action('woocommerce_after_main_content');
    ?>

</main>

<?php get_footer(); ?>
