<?php
/**
 * @version 9.4.0
 */

require_once plugin_dir_path(__FILE__) . 'product-variations.php';

defined( 'ABSPATH' ) || exit;

global $product;

// Check if the product is a valid WooCommerce product and ensure its visibility before proceeding.
if ( ! is_a( $product, WC_Product::class ) || ! $product->is_visible() ) {
    return;
}
?>
<div <?php wc_product_class( '', $product ); ?>>
    <div class="product-item">
        <a href="<?php the_permalink(); ?>" class="product-link">
            <?php 
                $gallery_image_ids = $product->get_gallery_image_ids();
                $hasSecondImage = count($gallery_image_ids) > 0 && isset($gallery_image_ids[0]) && wp_get_attachment_url($gallery_image_ids[0]);
            ?>
            <div class="dd-product-image<?php echo $hasSecondImage ? ' has-second-image' : ''; ?>">
                <div class="dd-product-image-labels">
                    <?php if ($product->is_on_sale()) { ?>
                        <div class="dd-product-on-sale-label">Promocja</div>
                    <?php } ?>
                    <?php if ($product->get_date_created() !== null && $product->get_date_created() > (new DateTime())->modify('-14 days')) { ?>
                        <div class="dd-product-new-label">Nowość</div>
                    <?php } ?>
                </div>
                
                <?php woocommerce_template_loop_product_thumbnail(); ?>

                <?php 
                if (count($gallery_image_ids) > 0) {
                    $second_image_url = esc_url(wp_get_attachment_image_url($gallery_image_ids[0], 'woocommerce_thumbnail'));
                    echo '<img data-src="' . $second_image_url . '" alt="" class="dd-product-second-image lazy-hidden">';
                }
                ?>
            </div>
            <h2 class="product-title"><?php the_title(); ?></h2>
            <?php dd_display_product_price($product); ?>
        </a>
        <div class="product-variations">
            <?php dd_product_variations($product); ?>
            <?php dd_display_add_to_cart_button($product); ?>
        </div>
    </div>
</div>