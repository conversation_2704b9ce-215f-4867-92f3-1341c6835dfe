<?php

/**
 * @version 9.4.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

require_once get_template_directory() . '/woocommerce/product-variations.php';

// Sprawdź, czy użytkownik jest zalogowany
if ( ! $checkout->is_registration_enabled() && $checkout->is_registration_required() && ! is_user_logged_in() ) {
    echo esc_html( apply_filters( 'woocommerce_checkout_must_be_logged_in_message', __( 'You must be logged in to checkout.', 'woocommerce' ) ) );
    return;
}

// do_action( 'woocommerce_before_checkout_form', $checkout );

?>

<form name="checkout" method="post" class="checkout woocommerce-checkout" action="<?php echo esc_url( wc_get_checkout_url() ); ?>" enctype="multipart/form-data" aria-label="<?php echo esc_attr__( 'Checkout', 'woocommerce' ); ?>">

<div class="checkout-container">
    <div class="checkout-left">
        <!-- Akordeon: Szczegóły wysyłki -->
        <div class="checkout-accordion-item">
            <div class="checkout-accordion-header" id="accordion-billing-address">
                <div>
                    <?php esc_html_e( 'Dane rozliczeniowe', 'woocommerce' ); ?>
                </div>
                <div>
                    <i class="fa-solid fa-chevron-down"></i>
                </div>
            </div>
            <div class="checkout-accordion-content">
                <?php do_action( 'woocommerce_checkout_billing' ); ?>
                <?php do_action( 'woocommerce_checkout_shipping' ); ?>
                <div class="checkout-row">
                    <button type="button" id="save-shipping-address" class="checkout-save-button"><?php esc_html_e( 'Wybierz sposób wysyłki', 'woocommerce' ); ?></button>
                </div>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                var saveButton = document.getElementById('save-shipping-address');
                var shippingAccordion = document.getElementById('shipping-accordion');
                var shippingContent = shippingAccordion.querySelector('.checkout-accordion-content');

                // Event listener for "Zapisz adres" button click
                saveButton.addEventListener('click', function() {
                    // Open the shipping accordion
                    shippingContent.style.display = 'block';
                    jQuery('#accordion-billing-address').click();
                    jQuery([document.documentElement, document.body]).animate({
                        scrollTop: $('body').offset().top
                    }, 500);
                });
            });
        </script>

        <!-- Akordeon: Sposób wysyłki -->
        <div class="checkout-accordion-item" id="shipping-accordion">
            <div class="checkout-accordion-header" id="shipping-accordion-header">
                <div>
                    <?php esc_html_e( 'Sposób wysyłki', 'woocommerce' ); ?>
                </div>
                <div>
                    <i class="fa-solid fa-chevron-down"></i>
                </div>
            </div>
    <div class="checkout-accordion-content">
        <div id="dd-checkout-shipping-section">
            <?php do_action( 'woocommerce_review_order_before_shipping' ); ?>

            <div id="shipping_methods">
            <?php foreach ( WC()->shipping->get_packages() as $i => $package ) : ?>
            <div class="shipping-methods-wrapper">
                <?php
                    $available_methods = $package['rates'];
                    $i = 0;
                    foreach ( $available_methods as $method ) : ?>
                <button class="shipping-method-button <?php if ($i === 0) echo 'selected';?>" data-shipping-method="<?php echo esc_attr( $method->id ); ?>" type="button">
                    <?php
                        echo esc_html( $method->label );
                        $methodTax = current($method->taxes) !== false ? current($method->taxes) : 0;
                    ?>
                    <span class="price"><?php echo wc_price( $method->cost + $methodTax); ?></span>
                </button>
                <?php
                    $i++;
                    endforeach;
                ?>
            </div>
            <?php endforeach; ?>
        </div>
        <div id="dd-inpost-check-paczkomat">
            <div class="easypack_show_geowidget" id="easypack_show_geowidget" style="display: none;">Wybierz miejsce odbioru paczki <i class="fa-regular fa-map"></i></div>
            <div id="selected-parcel-machine" class="selected-parcel-machine" style="display: none;">
                <div><span class="easypack-visible-point-header italic" style="font-weight: bold">
                        Wybrany paczkomat:                </span></div>
                <span class="easypack-visible-point-description" id="selected-parcel-machine-id"></span>

                <input type="hidden" id="parcel_machine_id" name="parcel_machine_id" class="parcel_machine_id" value="">
                <input type="hidden" id="parcel_machine_desc" name="parcel_machine_desc" class="parcel_machine_desc" value="">
            </div>
        </div>

        <input type="hidden" name="shipping_method[0]" data-index="0"  checked="checked">
            <?php do_action( 'woocommerce_review_order_after_shipping' ); ?>
        <div class="checkout-row">
            <button type="button" id="save-shipping-methods" class="checkout-save-button">Wybierz metodę płatności</button>
        </div>

    </div>
</div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const shippingButtons = document.querySelectorAll('.shipping-method-button');
        const geoWidget = document.getElementById('easypack_show_geowidget');

        shippingButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Sprawdź, czy metoda to "InPost Paczkomat"
                if (this.dataset.shippingMethod === 'easypack_parcel_machines:6') { // ID metody paczkomatu
                    geoWidget.style.display = 'block'; // Pokaż geowidget
                } else {
                    geoWidget.style.display = 'none'; // Ukryj geowidget
                }
            });
        });
    });
    document.addEventListener('DOMContentLoaded', function() {
    const shippingButtons = document.querySelectorAll('.shipping-method-button');
    const selectedParcelMachineDiv = document.getElementById('selected-parcel-machine');

    shippingButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Sprawdź, czy metoda to "InPost Paczkomat"
            if (this.dataset.shippingMethod === 'easypack_parcel_machines:6') { // ID metody paczkomatu
                selectedParcelMachineDiv.style.display = 'block'; // Pokaż div z paczkomatem
            } else {
                selectedParcelMachineDiv.style.display = 'none'; // Ukryj div z paczkomatem
            }
            });
        });
    });

    document.addEventListener('DOMContentLoaded', function() {
        var saveButton = document.getElementById('save-shipping-methods');
        var shippingAccordion = document.getElementById('payment-methods');
        var shippingContent = shippingAccordion.querySelector('.checkout-accordion-content');

        // Event listener for "Zapisz adres" button click
        saveButton.addEventListener('click', function() {
            // Open the shipping accordion
            shippingContent.style.display = 'block';
            jQuery('#shipping-accordion-header').click();
            jQuery([document.documentElement, document.body]).animate({
                scrollTop: $('body').offset().top
            }, 500);

        });
    });

    </script>

    <!-- Akordeon: Metoda płatności -->
    <div class="checkout-accordion-item" id="payment-methods">
        <div class="checkout-accordion-header">
            <div>
                <?php esc_html_e( 'Metoda płatności', 'woocommerce' ); ?>
            </div>
            <div>
                <i class="fa-solid fa-chevron-down"></i>
            </div>
        </div>

        <div class="checkout-accordion-content">
            <div id="payment_methods">
                <?php
                /** @var WC_Payment_Gateway[] $available_gateways */
                $available_gateways = WC()->payment_gateways->get_available_payment_gateways();

                if ( ! empty( $available_gateways ) ) : ?>
                    <ul class="wc_payment_methods payment_methods methods">
                        <?php foreach ( $available_gateways as $gateway ) : ?>
                            <li class="wc_payment_method payment_method_<?php echo esc_attr( $gateway->id ); ?>">
                                <button type="button" class="payment-method-button" data-payment-method="<?php echo esc_attr( $gateway->id ); ?>">
                                    <?php echo wp_kses_post( $gateway->get_title() ); ?>
                                    <?php echo $gateway->get_icon(); ?>
                                </button>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php else : ?>
                    <p><?php esc_html_e( 'Brak dostępnych metod płatności.', 'woocommerce' ); ?></p>
                <?php endif; ?>
                <?php foreach ( $available_gateways as $gateway ) : ?>
                        <?php if ( $gateway->has_fields() || $gateway->get_description() ) : ?>
                            <div style="display: none" class="payment_info payment_info_<?php echo esc_attr( $gateway->id ); ?>">
                                <?php $gateway->payment_fields(); ?>
                            </div>
                        <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
        <script>
            jQuery(document).ready(function($) {
                $('.payment-method-button').on('click', function() {
                    $('.payment-method-button').removeClass('selected');
                    $(this).addClass('selected');

                    // Ukryj wszystkie pola związane z metodami płatności
                    $('.payment_info').hide();

                    // Pokaż pole wybranej metody płatności
                    var methodId = $(this).data('payment-method');
                    $('.payment_info.payment_info_' + methodId).show();

                    // Ustaw wartość ukrytego pola płatności
                    let payment_method_input = $('input[name="payment_method"]');
                    payment_method_input.val(methodId).change();
                    payment_method_input.attr('id', 'payment_method_' + methodId);

                    // Zaktualizuj koszyk po wyborze metody
                    $('body').trigger('update_checkout');
                });
            });
        </script>
        <input type="radio" class="input-radio" name="payment_method" value="" data-order_button_text="">
    </div>

        <!-- Akordeon: Kod rabatowy -->
        <div class="checkout-accordion-item">
            <div class="checkout-accordion-header">
                <div>
                    <?php esc_html_e( 'Kod rabatowy', 'woocommerce' ); ?>
                </div>
                <div>
                    <i class="fa-solid fa-chevron-down"></i>
                </div>
            </div>
            <div class="checkout-accordion-content">
                <div id="coupon-errors"></div>
                <div class="coupon-form" style="margin-bottom:20px;">
                    <p>Wpisz kupon poniżej</p>
                    <p class="form-row form-row-first woocommerce-validated">
                        <input type="text" name="coupon_code" class="input-text" placeholder="Kod kuponu" id="coupon_code" value="">
                    </p>
                    <p class="form-row form-row-last">
                        <button type="button" class="button dd-checkout-button" name="apply_coupon" value="">Zatwierdź kupon</button>
                    </p>
                    <div class="clear"></div>
                </div>
            </div>
        </div>
        <script type="text/javascript">
            jQuery( function($){
                if (typeof wc_checkout_params === 'undefined')
                    return false;

                var couponCode = '';

                $('input[name="coupon_code"]').on( 'input change', function(){
                    couponCode = $(this).val();
                });

                $('button[name="apply_coupon"]').on( 'click', function(){
                    $.ajax({
                        type: 'POST',
                        url: wc_checkout_params.ajax_url,
                        data: {
                            'action': 'apply_checkout_coupon',
                            'coupon_code': couponCode,
                        },
                        success: function (response) {
                            jQuery('body').trigger('update_checkout'); // Refresh checkout
                            $('#coupon-errors').html(''); // Remove other notices
                            $('input[name="coupon_code"]').val(''); // Empty coupon code input field
                            $('#coupon-errors').html(response); // Display notices
                        }
                    });
                });
            });
        </script>
        </div>
<div class="checkout-right">
    <div class="dd-checkout-right-panel">
        <h2>PODSUMOWANIE ZAMÓWIENIA</h2>

        <div id="dd-checkout-items-summary">
                <ul class="cart-items">
                    <?php foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) :
                        /** @var WC_Product $_product */
                        $_product = $cart_item['data'];
                        $product_id = $cart_item['product_id'];
                        $product_price = $_product->get_price_html();

                        $product_image_url = get_the_post_thumbnail_url( $product_id, 'thumbnail' );
                        $variation_attributes = null;
                        if ($_product->is_type('variation')) {
                            /** @var WC_Product_Variation $_product */
                            $variation_attributes = $_product->get_attribute_summary();
                        }
                        ?>
                        <li class="cart-item" data-item-key="<?php echo esc_attr( $cart_item_key ); ?>">
                            <div class="cart-item-header">
                                <div class="cart-item-image">
                                    <?php
                                    $image_url = $_product->is_type('variation') && $_product->get_image_id()
                                        ? wp_get_attachment_image_url($_product->get_image_id(), 'thumbnail')
                                        : get_the_post_thumbnail_url($product_id, 'thumbnail');

                                    echo '<img src="' . esc_url($image_url) . '" alt="' . esc_attr($_product->get_name()) . '">';
                                    ?>
                                </div>
                                <div class="cart-item-details">
                                    <a href="<?php echo esc_url(get_permalink($product_id)); ?>" target="_blank">
                                        <span class="product-name">
                                            <?php echo esc_html($_product->is_type('variation') ? wc_get_product($_product->get_parent_id())?->get_name() : $_product->get_name()); ?>
                                        </span>
                                        <div class="cart-item-column">
                                            <?php if (!empty($variation_attributes)) : ?>
                                                <span class="product-attributes">
                                                    <?php echo $variation_attributes ?>
                                                </span>
                                            <?php endif; ?>
                                            <div class="cart-price" style="width: <?php echo $_product->is_type('variation') ? 'auto' : '100%'; ?>;">
                                                <span class="product-price"><?php echo $product_price; ?></span>
                                            </div>
                                        </div>
                                    </a>
                                    <div class="cart-item-footer">
                                        <div class="cart-quantity">
                                            <input type="number" name="cart[<?php echo $cart_item_key; ?>][qty]" value="<?php echo $cart_item['quantity']; ?>" min="1" max="<?php echo $_product->get_max_purchase_quantity(); ?>">
                                        </div>
                                        <div class="cart-remove">
                                            <a href="javascript:void(0);" class="remove-item"><i class="fa fa-trash"></i></a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>


                    <?php endforeach; ?>
                </ul>

        </div>
        <div id="order_review" class="woocommerce-checkout-review-order">
            <?php do_action( 'woocommerce_checkout_order_review' ); ?>
        </div>
    </div>
</div>
</form>
<?php do_action( 'woocommerce_after_checkout_form', $checkout ); ?>
<script>
    jQuery(document).ready(function($) {
        jQuery('#accordion-billing-address').click();
        setTimeout(
            function()
            {
                jQuery('#accordion-billing-address').click();
            }, 1000);

        var selectedShippingMethod = null;

        // Funkcja do wyboru metody wysyłki
        $('.shipping-method-button').on('click', function() {
            $('.shipping-method-button').removeClass('selected');
            $(this).addClass('selected');

            selectedShippingMethod = $(this).data('shipping-method');

            // Ustaw metodę wysyłki w formularzu
            $('input[name="shipping_method[0]"]').val(selectedShippingMethod).change();

            // Wywołaj aktualizację koszyka
            jQuery('body').trigger('update_checkout');
        });
    });

    jQuery(document).ready(function($) {
        $('.cart-quantity input').on('blur', function () {
            setTimeout(
                function()
                {
                    jQuery('body').trigger('update_checkout');
                }, 1000);
        });
    });


</script>