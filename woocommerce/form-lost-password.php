<?php
/**
 * Lost password form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/form-lost-password.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.2.0
 */

 /*
Template Name: Odzyskiwanie
*/

get_header()?>
<?php
defined( 'ABSPATH' ) || exit;
?>

<main>
<div class="logowanie-container">

<section class="logowanie">
    <h1 class="logowanie-header"><?php esc_html_e('<PERSON>e pami<PERSON><PERSON><PERSON> hasła?', 'woocommerce'); ?></h1>
    <p class="logowanie-description">Nie ma problemu. Podaj nam swój adres email, a wyślemy Ci link do resetowania hasła,<br> kt<PERSON>ry pozwoli Ci wprowadzić nowe.</p>

    <div class="odzyskiwanie-box">
	<form method="post" class="woocommerce-ResetPassword lost_reset_password">
<?php do_action( 'woocommerce_before_lost_password_form' ); ?>
<p class="woocommerce-form-row woocommerce-form-row--first form-row form-row-first">
<label for="user_login"><?php esc_html_e( 'Email', 'woocommerce' ); ?>&nbsp;<span class="required" aria-hidden="true">*</span><span class="screen-reader-text"><?php esc_html_e( 'Required', 'woocommerce' ); ?></span></label>
<input class="woocommerce-Input woocommerce-Input--text input-text" type="text" name="user_login" id="user_login" autocomplete="username" required aria-required="true" />
</p>

<div class="clear"></div>

<?php do_action( 'woocommerce_lostpassword_form' ); ?>

<p class="woocommerce-form-row form-row">
<input type="hidden" name="wc_reset_password" value="true" />
<button type="submit" class="woocommerce-button button<?php echo esc_attr( wc_wp_theme_get_element_class_name( 'button' ) ? ' ' . wc_wp_theme_get_element_class_name( 'button' ) : '' ); ?>" value="<?php esc_attr_e( 'Reset password', 'woocommerce' ); ?>"><?php esc_html_e( 'Wyślij link do resetowania hasła', 'woocommerce' ); ?></button>
</p>

<?php wp_nonce_field( 'lost_password', 'woocommerce-lost-password-nonce' ); ?>

</form>
	</div>
</section>

</main>

<?php do_action( 'woocommerce_after_lost_password_form' ); get_footer()?>