<?php
/*
Template Name: Reje<PERSON><PERSON>ja
*/
ob_start();
get_header();

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['register'])) {
    $first_name = sanitize_text_field($_POST['first_name']);
    $last_name = sanitize_text_field($_POST['last_name']);
    $email = sanitize_email($_POST['email']);
    $password = $_POST['password'];
    $password_confirm = $_POST['password_confirm'];
    $terms = isset($_POST['terms']) ? true : false;

    $errors = new WP_Error();

    // Sprawdzanie błędów
    if ($password !== $password_confirm) {
        $errors->add('password_mismatch', 'Hasła nie są zgodne.');
    }

    if (!is_email($email)) {
        $errors->add('invalid_email', 'Adres email jest nieprawidłowy.');
    }

    if (email_exists($email)) {
        $errors->add('email_exists', 'Adres email jest już zarejestrowany.');
    }

    if (!$terms) {
        $errors->add('terms_not_accepted', 'Musisz zaakceptować regulamin.');
    }

    if (empty($errors->errors)) {
        $user_id = wp_create_user($email, $password, $email);

        if (is_wp_error($user_id)) {
            $errors->add('user_creation_error', $user_id->get_error_message());
        } else {
            // Aktualizacja imienia i nazwiska
            wp_update_user(array(
                'ID' => $user_id,
                'first_name' => $first_name,
                'last_name' => $last_name
            ));

            wp_redirect(home_url('/logowanie/?registration=success'));
            exit;
        }
    }
}

?>

<section class="rejestracja">
    <?php wc_print_notices(); ?>
    <h1 class="rejestracja-header"><?php esc_html_e('Rejestracja', 'woocommerce'); ?></h1>
    <p class="rejestracja-description"><?php esc_html_e('Utwórz nowe konto, aby uzyskać dostęp do naszego serwisu.', 'woocommerce'); ?></p>

    <div class="rejestracja-box">
        <?php
        if (!empty($errors->errors)) {
            foreach ($errors->get_error_messages() as $message) {
                echo '<div class="woocommerce-error">' . esc_html($message) . '</div>';
            }
        }

        if (isset($_GET['registration']) && $_GET['registration'] == 'success') {
            echo '<div class="woocommerce-message">' . esc_html('Rejestracja zakończona sukcesem! Możesz teraz <a href="' . esc_url(home_url('/logowanie')) . '">zalogować się</a>.', 'woocommerce') . '</div>';
        }


        ?>

        <form class="woocommerce-form woocommerce-form-register register" method="post" action="">
            <p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
                <label for="reg_first_name"><?php esc_html_e('Imię', 'woocommerce'); ?></label>
                <input type="text" class="woocommerce-Input woocommerce-Input--text input-text" name="first_name" id="reg_first_name" autocomplete="given-name" required aria-required="true" />
            </p>

            <p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
                <label for="reg_last_name"><?php esc_html_e('Nazwisko', 'woocommerce'); ?></label>
                <input type="text" class="woocommerce-Input woocommerce-Input--text input-text" name="last_name" id="reg_last_name" autocomplete="family-name" required aria-required="true" />
            </p>

            <p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
                <label for="reg_email"><?php esc_html_e('Email', 'woocommerce'); ?></label>
                <input type="email" class="woocommerce-Input woocommerce-Input--text input-text" name="email" id="reg_email" autocomplete="email" required aria-required="true" />
            </p>

            <p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
                <label for="reg_password"><?php esc_html_e('Hasło', 'woocommerce'); ?></label>
                <input type="password" class="woocommerce-Input woocommerce-Input--text input-text" name="password" id="reg_password" autocomplete="new-password" required aria-required="true" />
            </p>

            <p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
                <label for="reg_password_confirm"><?php esc_html_e('Potwierdź hasło', 'woocommerce'); ?></label>
                <input type="password" class="woocommerce-Input woocommerce-Input--text input-text" name="password_confirm" id="reg_password_confirm" autocomplete="new-password" required aria-required="true" />
            </p>

            <p class="woocommerce-form-row-checkbox">
                <label class="woocommerce-form__label woocommerce-form__label-for-checkbox woocommerce-form-register__terms">
                    <input class="woocommerce-form__input woocommerce-form__input-checkbox" name="terms" type="checkbox" id="terms" required aria-required="true" />
                    <span><?php esc_html_e('Akceptuję Regulamin i wyrażam zgodę na przetwarzanie moich danych zgodnie z Polityką prywatności', 'woocommerce'); ?></span>
                </label>
            </p>

            <p class="form-row">
                <button type="submit" class="woocommerce-button button woocommerce-form-register__submit" name="register" value="<?php esc_attr_e('Zarejestruj się', 'woocommerce'); ?>"><?php esc_html_e('Zarejestruj się', 'woocommerce'); ?></button>
            </p>
        </form>

        <div class="form-row form-row-register">
            <span class="register-link">Posiadasz już konto? <a href="<?php echo esc_url(home_url('/logowanie/')); ?>"><?php esc_html_e('Zaloguj się', 'woocommerce'); ?></a></span>
        </div>
    </div>
</section>

<?php
ob_end_flush();
get_footer(); ?>


