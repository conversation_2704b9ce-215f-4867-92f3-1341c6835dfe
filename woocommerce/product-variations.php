<?php

use DD\FitospaTheme\WooCommerceUnitPrice;

/**
 * Wyświetlanie wariantów oraz przycisku na stronie
 *
 * @param WC_Product_Variable $product
 */
function dd_single_product_variations($product) {
    if ($product->is_type('variable')) {
        echo '<div class="product-variations">';
        $available_variations = $product->get_variation_attributes();

        foreach ($available_variations as $attribute => $values) {
            $attribute_name = wc_attribute_label($attribute);
            $translated_attribute = dd_translate_label($attribute_name);
            echo '<p><span>Wybierz ' . esc_html($translated_attribute) . '</span></p>';

            dd_render_variation_buttons($product->get_available_variations('objects'), true);

            break;
        }

        echo '</div>';
    }
}

/**
 * Wyświetlanie wariantów oraz przycisku na podstronach
 *
 */
function dd_product_variations($product) {
    if ($product->is_type('variable')) {
        $available_variations = $product->get_available_variations('objects');

        if (!empty($available_variations)) {
            dd_render_variation_buttons($available_variations);
        }
    }
}

/**
 * Tłumaczenie wc_attribute_label
 *
 */
function dd_translate_label($attribute_name) {
    $translations = [
        'waga' => 'wagę',
    ];

    return $translations[mb_strtolower($attribute_name)] ?? mb_strtolower($attribute_name);
}

/**
 * Zwraca etykietę dla jednostek na podstawie atrybutów lub wariantów.
 *
 * @param array $data Lista atrybutów lub wariantów.
 * @param bool $is_variations ustala czy dane dotyczą wariantów (true) czy atrybutów (false).
 * @return string Etykieta dla jednostek.
 */
function dd_get_label($data, $is_variations = false) {
    $attributes_with_labels = [];

    if ($is_variations) {
        foreach ($data as $variation) {
            foreach ($variation['attributes'] as $attribute => $value) {
                if (!is_string($value)) {
                    continue;
                }

                $attribute_name = wc_attribute_label($attribute);
                $value = preg_replace('/(\d+)(-?)([a-zA-Z]+)/', '$1 $3', $value);
                $attributes_with_labels[] = $attribute_name . ': <b>' . $value . '</b>';
            }
        }

        if (empty($attributes_with_labels)) {
            return 'Wybierz wariant';
        }

    } else {
        foreach ($data as $attribute => $value) {
            if (!is_string($value)) {
                continue;
            }

            $attribute_name = wc_attribute_label($attribute);
            $value = preg_replace('/(\d)([a-zA-Z]+)/', '$1 $2', $value);
            $attributes_with_labels[] = $attribute_name . ': <b>' . $value . '</b>';
        }
    }

    return implode(', ', $attributes_with_labels);
}

/**
 * Funkcja wyświetlająca przycisk wariantu.
 *
 * @param array<WC_Product_Variation> $variations Dane o wariancie.
 */
function dd_render_variation_buttons(array $variations, bool $is_single_product = false): void
{
    $total_variations = count($variations);

    echo '<div class="variations">';

    foreach ($variations as $variation) {
        $variation_id = $variation->get_id();
        $attributes = $variation->get_attributes();

        $attribute_names = [];
        foreach ($attributes as $value) {
            $values = preg_replace('/(\d+)(-?)([a-zA-Z]+)/', '$1 $3', $value);
            $attribute_names[] = $values;
        }

        $variation_description = $variation->get_description();

        $price_html = $variation->get_price_html();
        $price_number = (float) $variation->get_price();

        $is_on_sale = $variation->is_on_sale();

        $sku = $variation->get_sku();

        $stock_quantity = $variation->get_stock_quantity();

        $unit_price_text = WooCommerceUnitPrice::getVariationUnitPrice($variation_id);

        $button_class = 'variation-button';
        if ($stock_quantity <= 0) {
            $button_class .= ' out-of-stock';
        }

        $data_image_url = wp_get_attachment_image_url($variation->get_image_id(), 'large');
        $button_disabled = '';

        if (!$is_single_product) {
            $button_disabled = $stock_quantity <= 0 ? 'disabled' : '';
        }

        if ($total_variations) {
            $button_class .= " variation-" . $total_variations;
        }

        echo '<button class="' . esc_attr($button_class) . '"
                data-variation-id="' . esc_attr($variation_id) . '"
                data-description="' . esc_attr($variation_description) . '"
                data-image-url="' . esc_attr($data_image_url) . '"
                data-price-html="' . esc_attr($price_html) . '"
                data-price-number="' . esc_attr($price_number) . '"
                data-sku="' . esc_attr($sku) . '"
                data-is-on-sale="' . (int) $is_on_sale . '"
                data-stock-quantity="' . esc_attr($stock_quantity) . '"
                data-unit-price="' . esc_attr($unit_price_text) . '" ' . esc_attr($button_disabled) . '>';

        echo implode(', ', $attribute_names);
        echo '</button>';
    }
    echo '</div>';
}

function dd_display_product_price($product) {
    if (!is_a($product, 'WC_Product')) {
        return;
    }
    $price_html = $product->get_price_html();

    echo '<p class="price">' . $price_html . '</p>';
}

/**
 * @param WC_Product $product
 */
function dd_display_product_omnibus_message($product) {
    if (!$product->is_type('variable')) {
        if (!$product->is_on_sale()) {
            return;
        }

        echo '<div class="dd-omnibus-message-wrapper">';
        do_action( 'iworks_omnibus_wc_lowest_price_message', $product->get_id() );
        echo '</div>';

        return;
    }

    echo '<div class="dd-omnibus-message-wrapper"></div>';

    foreach ($product->get_available_variations() as $variation) {
        echo "<div style='display: none;' class='dd-omnibus-message' data-variation-id='" . esc_attr($variation['variation_id']) . "'>";
        do_action( 'iworks_omnibus_wc_lowest_price_message', $variation['variation_id'] );
        echo "</div>";
    }
}

/**
 * @param WC_Product $product
 */
function dd_display_unit_price($product)
{
    $unit_price_html = WooCommerceUnitPrice::displayUnitPrice($product);

    echo $unit_price_html;
}

/**
 * @param WC_Product $product
 */
function dd_display_add_to_cart_button($product) {
    if (!is_a($product, 'WC_Product')) {
        echo 'Produkt nie został znaleziony lub jest nieprawidłowy.';
        return;
    }

    $showQuantityOnPage = is_single() && is_woocommerce();

    if ($product->is_type('variable')) {
        $variations = $product->get_children();
        $stock_quantity = 0;

        foreach ($variations as $variation_id) {
            $variation = wc_get_product($variation_id);
            if ($variation) {
                $stock_quantity += $variation->get_stock_quantity();
            }
        }
    } else {
        $stock_quantity = $product->get_stock_quantity();
    }

    $show_quantity = $showQuantityOnPage && $stock_quantity > 0;
    $show_button = $stock_quantity > 0;
    $button_class = $show_button ? 'single_add_to_cart_button button alt' : 'single_add_to_cart_button button alt disabled';
    $button_text = $show_button ? 'Dodaj do koszyka' : 'Produkt niedostępny';
    $button_disabled = $show_button ? '' : 'disabled';

    $product_name = $product->get_name();
    $product_price = (float) $product->get_price();
    $product_sku = $product->get_sku();

    ?>
    <form class="cart" action="<?php echo esc_url(wc_get_cart_url()); ?>" method="post" enctype="multipart/form-data">
        <div class="quantity-and-cart">
            <?php if ($show_quantity): ?>
                <div class="quantity" style="display: flex;">
                    <button type="button" class="minus">-</button>
                    <input type="number" id="quantity" class="input-text qty text" step="1" min="1" max="<?php echo esc_attr($stock_quantity); ?>" name="quantity" value="1" title="Qty" size="4" pattern="[0-9]*" inputmode="numeric" />
                    <button type="button" class="plus">+</button>
                </div>
            <?php endif; ?>

            <button
                    type="submit"
                    name="add-to-cart"
                    value="<?php echo esc_attr($product->get_id()); ?>"
                    class="<?php echo esc_attr($button_class); ?>" <?php echo esc_attr($button_disabled); ?>
                    data-product-name="<?php echo esc_attr($product_name); ?>"
                    data-product-price="<?php echo esc_attr($product_price); ?>"
                    data-sku="<?php echo esc_attr($product_sku); ?>"
            >
                <?php echo esc_html($button_text); ?>
            </button>
        </div>
    </form>
    <?php
}

/**
 * Wyświetlanie dostępności produktów dla wariantów
 */
function dd_display_stock_availability($product) {
    if ($product->is_type('variable')) {
        $available_variations = $product->get_available_variations();
        foreach ($available_variations as $variation) {
            $variation_product = wc_get_product($variation['variation_id']);
            $stock_quantity = $variation_product->get_stock_quantity();
            $availability_text = '';
            $availability_class = '';

            if ($stock_quantity <= 0) {
                $availability_text = 'Produkt aktualnie niedostępny';
                $availability_class = 'dd-quantity-not-available';
            } elseif ($stock_quantity <= 5 && $stock_quantity > 0) {
                $availability_text = 'Produkt dostępny - zostało tylko <b>' . $stock_quantity . '</b> szt.';
            } else {
                $availability_text = 'Produkt dostępny';
            }

            echo '<p class="dd-quantity-available ' . esc_attr($availability_class) . '"
                data-variation-id="' . esc_attr($variation['variation_id']) . '"
                data-stock-quantity="' . esc_attr($stock_quantity) . '"
                style="display:none;">' . $availability_text . '</p>';
        }
    } else {
        if ($product->get_stock_quantity() <= 0) {
            echo '<p class="dd-quantity-available dd-quantity-not-available">Produkt aktualnie niedostępny</p>';
        } elseif ($product->get_stock_quantity() <= 5 && $product->get_stock_quantity() > 0) {
            echo '<p class="dd-quantity-available">Produkt dostępny - zostało tylko <b>' . $product->get_stock_quantity() . ' </b> szt.</p>';
        } else {
            echo '<p class="dd-quantity-available">Produkt dostępny</p>';
        }
    }
}

?>