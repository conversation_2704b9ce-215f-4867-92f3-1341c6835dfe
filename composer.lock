{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "626eb06339a7ac7c129291bc4a4433db", "packages": [], "packages-dev": [{"name": "php-stubs/woocommerce-stubs", "version": "v10.0.2", "source": {"type": "git", "url": "https://github.com/php-stubs/woocommerce-stubs.git", "reference": "54123e2fa2a07b11d03fe15f27fb7a64d970a9df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-stubs/woocommerce-stubs/zipball/54123e2fa2a07b11d03fe15f27fb7a64d970a9df", "reference": "54123e2fa2a07b11d03fe15f27fb7a64d970a9df", "shasum": ""}, "require": {"php-stubs/wordpress-stubs": "^5.3 || ^6.0"}, "require-dev": {"php": "~7.1 || ~8.0", "php-stubs/generator": "^0.8.0"}, "suggest": {"symfony/polyfill-php73": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "szepeviktor/phpstan-wordpress": "WordPress extensions for PHPStan"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WooCommerce function and class declaration stubs for static analysis.", "homepage": "https://github.com/php-stubs/woocommerce-stubs", "keywords": ["PHPStan", "static analysis", "woocommerce", "wordpress"], "support": {"issues": "https://github.com/php-stubs/woocommerce-stubs/issues", "source": "https://github.com/php-stubs/woocommerce-stubs/tree/v10.0.2"}, "time": "2025-07-14T17:13:11+00:00"}, {"name": "php-stubs/wordpress-stubs", "version": "v6.8.2", "source": {"type": "git", "url": "https://github.com/php-stubs/wordpress-stubs.git", "reference": "9c8e22e437463197c1ec0d5eaa9ddd4a0eb6d7f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-stubs/wordpress-stubs/zipball/9c8e22e437463197c1ec0d5eaa9ddd4a0eb6d7f8", "reference": "9c8e22e437463197c1ec0d5eaa9ddd4a0eb6d7f8", "shasum": ""}, "conflict": {"phpdocumentor/reflection-docblock": "5.6.1"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "nikic/php-parser": "^5.5", "php": "^7.4 || ^8.0", "php-stubs/generator": "^0.8.3", "phpdocumentor/reflection-docblock": "^5.4.1", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^9.5", "szepeviktor/phpcs-psr-12-neutron-hybrid-ruleset": "^1.1.1", "wp-coding-standards/wpcs": "3.1.0 as 2.3.0"}, "suggest": {"paragonie/sodium_compat": "Pure PHP implementation of libsodium", "symfony/polyfill-php80": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "szepeviktor/phpstan-wordpress": "WordPress extensions for PHPStan"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WordPress function and class declaration stubs for static analysis.", "homepage": "https://github.com/php-stubs/wordpress-stubs", "keywords": ["PHPStan", "static analysis", "wordpress"], "support": {"issues": "https://github.com/php-stubs/wordpress-stubs/issues", "source": "https://github.com/php-stubs/wordpress-stubs/tree/v6.8.2"}, "time": "2025-07-16T06:41:00+00:00"}, {"name": "phpstan/phpstan", "version": "2.1.21", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "1ccf445757458c06a04eb3f803603cb118fe5fa6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/1ccf445757458c06a04eb3f803603cb118fe5fa6", "reference": "1ccf445757458c06a04eb3f803603cb118fe5fa6", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "time": "2025-07-28T19:35:08+00:00"}, {"name": "szepeviktor/phpstan-wordpress", "version": "v2.0.2", "source": {"type": "git", "url": "https://github.com/szepeviktor/phpstan-wordpress.git", "reference": "963887b04c21fe7ac78e61c1351f8b00fff9f8f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/szepeviktor/phpstan-wordpress/zipball/963887b04c21fe7ac78e61c1351f8b00fff9f8f8", "reference": "963887b04c21fe7ac78e61c1351f8b00fff9f8f8", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "php-stubs/wordpress-stubs": "^6.6.2", "phpstan/phpstan": "^2.0"}, "require-dev": {"composer/composer": "^2.1.14", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.1", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.0", "szepeviktor/phpcs-psr-12-neutron-hybrid-ruleset": "^1.0", "wp-coding-standards/wpcs": "3.1.0 as 2.3.0"}, "suggest": {"swissspidy/phpstan-no-private": "Detect usage of internal core functions, classes and methods"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"SzepeViktor\\PHPStan\\WordPress\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WordPress extensions for PHPStan", "keywords": ["PHPStan", "code analyse", "code analysis", "static analysis", "wordpress"], "support": {"issues": "https://github.com/szepeviktor/phpstan-wordpress/issues", "source": "https://github.com/szepeviktor/phpstan-wordpress/tree/v2.0.2"}, "time": "2025-02-12T18:43:37+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.2.0"}