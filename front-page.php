<?php 
/*
Template Name: Strona główna
*/

use DD\FitospaTheme\WooCommerceProductListing;
use DD\FitospaTheme\HomepageSlides;

get_header(); ?>

<main>
    <section class="main-swipper">
        <div class="mainSwiper">
            <div class="main-wrapper swiper-wrapper">
                <?php
                $slides = HomepageSlides::getActiveSlides();

                foreach ($slides as $slide) :
                ?>
                <div class="swiper-slide">
                    <a href="<?php echo esc_url(home_url($slide['link'])); ?>" class="slide-link">
                        <span class="sr-only"><?php echo esc_attr($slide['alt']); ?></span>
                        <picture>
                            <?php if (!empty($slide['mobile_image'])): ?>
                                <source media="(max-width: 450px)" srcset="<?php echo esc_url($slide['mobile_image']); ?>">
                            <?php endif; ?>
                            <img src="<?php echo esc_url($slide['image']); ?>" alt="<?php echo esc_attr($slide['alt']); ?>">
                        </picture>
                    </a>
                </div>
                <?php endforeach; ?>
                <div class="swiper-pagination"></div>
            </div>
        </div>
    </section>

    <div class="dd-container">
        <section class="section-category">
            <div class="wrapper">
                <h2 class="section-subtitle">KATEGORIE</h2>
                <hr>
                <span class="subtitle-text desktop">Rozkochaj się w naszych produktach, na różne sposoby.</span>
            </div>
            <span class="subtitle-text mobile">Rozkochaj się w naszych produktach, na różne sposoby.</span>
            <div class="grid">
                <?php
                $selected_category_ids = explode( ',', get_theme_mod('dd_categories_ids', '') );

                $args = [
                    'taxonomy'   => 'product_cat',
                    'parent'     => 0,
                    'orderby'    => 'name',
                    'order'      => 'ASC',
                    'hide_empty' => false,
                    'include'    => $selected_category_ids,
                ];

                $product_categories = get_terms( $args );

                usort( $product_categories, function( $a, $b ) use ( $selected_category_ids ) {
                    $a_position = array_search( $a->term_id, $selected_category_ids );
                    $b_position = array_search( $b->term_id, $selected_category_ids );
                    return $a_position - $b_position;
                });

                if ( ! empty( $product_categories ) && ! is_wp_error( $product_categories ) ) :
                    foreach ( $product_categories as $category ) :
                        $category_link = get_term_link( $category );
                        $thumbnail_id  = get_term_meta( $category->term_id, 'thumbnail_id', true );
                        $image_url     = wp_get_attachment_url( $thumbnail_id ) ?: 'default-image-url.jpg';
                ?>
                <div class="category-item">
                    <a href="<?php echo esc_url( $category_link ); ?>" class="category-link">
                        <img class="category-image" src="<?php echo esc_url( $image_url ); ?>"
                            alt="<?php echo esc_attr( $category->name ); ?>" />
                        <div class="category-content">
                            <hr>
                            <div class="insider">
                                <span class="category-title"><?php echo esc_html( $category->name ); ?></span>
                                <?php get_template_part('templates/parts/category-button', get_post_format()); ?>
                            </div>
                        </div>
                    </a>
                </div>
                <?php
                    endforeach;
                endif;
                ?>

                <div class="category-item">
                    <a href="<?php echo esc_url(home_url('sklep'));?>" class="category-link">
                        <span class="category-title">Sklep</span>
                    </a>
                </div>
            </div>
        </section>

    <section class="section-bestseller">
        <div class="wrapper">
            <h2 class="section-title">Bestsellery</h2>
            <div class="swiper-button-container">
                <div class="bestsellers swiper-button-prev"></div>
                <div class="bestsellers swiper-button-next"></div>
            </div>
        </div>

        <div class="swiper <?php echo esc_attr('swiperBestsellers'); ?>">
            <div class="swiper-wrapper">
                <?php
                $best_sellers_ids = get_theme_mod('dd_best_sellers', '');

                $args = [
                    'posts_per_page' => 4,
                    'is_swiper'      => true,
                    'section_title'   => 'Bestsellery',
                ];

                if (!empty($best_sellers_ids)) {
                    $product_ids = explode(',', $best_sellers_ids);
                    $args['post__in'] = $product_ids;
                    $args['orderby'] = 'post__in';
                } else {
                    $args['orderby'] = 'meta_value_num';
                    $args['meta_key'] = 'total_sales';
                    $args['order'] = 'DESC';
                }

                echo WooCommerceProductListing::generateListing($args);
                ?>
            </div>
        </div>
    </section>
    </div>

    <?php get_template_part( 'templates/parts/advantages-section' ); ?>

    <section class="section-for">
        <div class="dd-container">
            <?php
            $product_categories = [
                287 => [
                    'title' => 'Coś dla CIAŁA',
                    'image' => get_template_directory_uri() . '/public/cialo-category.webp',
                    'option_key' => 'dd_body_products',
                    'items' => ['Oleje', 'Peelingi', 'Masła'],
                ],
                293 => [
                    'title' => 'Coś dla TWARZY',
                    'image' => get_template_directory_uri() . '/public/twarz-category.webp',
                    'option_key' => 'dd_face_products',
                    'items' => ['Kremy', 'Serum', 'Maseczki'],
                ],
                303 => [
                    'title' => 'Coś do KĄPIELI',
                    'image' => get_template_directory_uri() . '/public/kapiel-category.webp',
                    'option_key' => 'dd_bath_products',
                    'items' => ['Żele', 'Sole do kąpieli', 'Mydła'],
                ],
            ];

            foreach ($product_categories as $category_id => $category_info) :
                $product_ids = get_theme_mod($category_info['option_key'], '');

                $args = [
                    'posts_per_page' => 3,
                    'post_type'      => 'product',
                    'is_swiper'      => true,
                    'section_title'  => $category_info['title'],
                ];

                if (!empty($product_ids)) {
                    $args['post__in'] = explode(',', $product_ids);
                    $args['orderby'] = 'post__in';
                } else {
                    $args['tax_query'] = [
                        [
                            'taxonomy' => 'product_cat',
                            'field'    => 'id',
                            'terms'    => $category_id,
                            'operator' => 'IN',
                        ],
                    ];
                    $args['orderby'] = 'date';
                }

                $category_image = !empty($category_info['image']) ? esc_url($category_info['image']) : '';
                $category_title = esc_html($category_info['title']);
                ?>

                <div class="category-section">
                    <div class="category-image">
                        <?php if ($category_image) : ?>
                            <img src="<?php echo $category_image; ?>" alt="<?php echo $category_title; ?>">
                        <?php endif; ?>
                        <ul>
                            <?php foreach ($category_info['items'] as $item) : ?>
                                <li><?php echo esc_html($item); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <div class="category-content">
                        <div class="category-text">
                            <h2 class="category-title"><?php echo $category_title; ?></h2>
                            <a href="<?php echo get_term_link($category_id, 'product_cat'); ?>" class="main-button reverse desktop">Zobacz więcej</a>
                            <div class="swiper-button-container mobile">
                                <div class="for swiper-button-prev" data-swiper-id="<?php echo $category_id; ?>"></div>
                                <div class="for swiper-button-next" data-swiper-id="<?php echo $category_id; ?>"></div>
                            </div>
                        </div>

                        <div class="swiperFor swiper-category-<?php echo $category_id; ?>">
                            <div class="swiper-wrapper">
                                <?php echo WooCommerceProductListing::generateListing($args); ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </section>

    <?php get_template_part( 'templates/parts/awards-section' ); ?>

    <section class="opinie-section">
        <div class="dd-container">
            <h2 class="section-title">Opinie naszych klientów</h2>
            <?php echo do_shortcode('[trustindex no-registration=google]'); ?>
        </div>
    </section>

    <section class="section-inspired">
        <div class="dd-container">
            <div class="inspired-wrapper">
                <?php
                $video = [
                    'id'    => 't6TQofK-eZk',
                    'title' => 'Zainspiruj się i stwórz swoje domowe SPA',
                ];

                $embed_url = 'https://www.youtube.com/embed/' . esc_attr($video['id']) . '?rel=0&modestbranding=1';
                ?>
                <div class="inspired-image">
                    <h2 class="section-title"><?php echo esc_html($video['title']); ?></h2>
                    <div class="video-container">
                        <iframe class="responsive-iframe" src="<?php echo esc_url($embed_url); ?>"
                            title="<?php echo esc_attr($video['title']); ?>" frameborder="0"
                            loading="lazy"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen>
                        </iframe>
                    </div>
                </div>

                <div class="swiper swiperInspired">
                    <span class="section-subtitle">Wykorzystane produkty</span>
                    <div class="swiper-button-container">
                        <div class="swiper-button-prev"></div>
                        <div class="swiper-button-next"></div>
                    </div>
                    <div class="swiper-wrapper">
                        <?php
                        $inspired_products = get_theme_mod('dd_inspired_products', '');

                        $args = [
                            'post_type'      => 'product',
                            'posts_per_page' => 6,
                            'orderby'        => 'date',
                            'order'          => 'DESC',
                            'is_swiper'      => true,
                            'section_title'  => 'Wykorzystane produkty',
                        ];

                        if (!empty($inspired_products)) {
                            $product_ids = explode(',', $inspired_products);
                            $args['post__in'] = $product_ids;
                            $args['orderby'] = 'post__in';
                        }

                        echo WooCommerceProductListing::generateListing($args);
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="section-cards">
        <div class="wrapper">
            <?php
            $cards_products = get_theme_mod('dd_cards_products', '');
            $product_ids = explode(';', $cards_products);
            $cards = [
                [
                    'title' => 'ROKITNIKOWE LOVE',
                    'subtitle' => 'Regeneracja, odbudowa i naturalne rozświetlenie',
                    'image' => '/public/rokitnikowe-love.webp',
                    'description' => [
                        'Olej z rokitnika i marchwi – intensywnie regeneruje skórę, przyspiesza odbudowę i wyrównuje koloryt.',
                        'Moc witamin i antyoksydantów – witaminy A, C, E oraz karotenoidy wzmacniają skórę, nadając jej zdrowy blask.',
                        'Zapach egzotycznych wakacji! Cytrusowo-ziołowa kompozycja olejków eterycznych – trawa cytrynowa i werbena egzotyczna pobudzają zmysły, dodając świeżości.',
                        'Arometerapeutyczny zastrzyk energii – odświeżający zapach rewitalizuje, działa antydepresyjnie, poprawia samopoczucie i wspomaga walkę ze stresem.'
                    ],
                    'product_ids' => !empty($product_ids[0]) ? explode(',', $product_ids[0]) : [2],
                    'black_text' => false,
                    'link' => '/produkty/linie-serie/rokitnikowe-love-rokitnik-marchew/',
                ],
                [
                    'title' => 'OWOCE MIŁOŚCI',
                    'subtitle' => 'Ujędrnienie, elastyczność i pobudzenie zmysłów',
                    'image' => '/public/owoce-milosci.webp',
                    'description' => [
                        'Olej marula i olej z pestek jabłoni – głęboko nawilżają, regenerują i poprawiają jędrność skóry.',
                        'Składniki aktywne wspomagają produkcję kolagenu, wygładzają i uelastyczniają skórę.',
                        'Zapach miłości! Afrodyzjakalna kompozycja olejków eterycznych – róża damasceńska, bergamotka, wanilia, drzewo sandałowe i cedr pobudzają zmysły i wspomagają libido.',
                        'Aromaterapia odprężająca i pobudzająca zmysły – poprawia nastrój, relaksuje i działa rozluźniająco.'
                    ],
                    'product_ids' => !empty($product_ids[1]) ? explode(',', $product_ids[1]) : [2],
                    'black_text' => false,
                    'link' => '/produkty/linie-serie/owoce-milosci-marula-jablon/',
                ],
                [
                    'title' => 'ELIKSIR ŻYCIA',
                    'subtitle' => 'Odnowa, rozjaśnianie i działanie przeciwstarzeniowe.',
                    'image' => '/public/eliksir-zycia.webp',
                    'description' => [
                        'Olej perilla i ekstrakt z lukrecji – intensywnie nawilżają, łagodzą podrażnienia, działają przeciwstarzeniowo.',
                        'Glabradyna działa depigmentacyjnie rozjaśnia przebarwienia i wyrównuje koloryt skóry, a kwas glicyretynowy wspomaga redukcję obrzęków poprawiając krążenie limfatyczne.',
                        'Zapach witalności! Kompozycja olejków eterycznych – geranium, mięta, amyris, limonka, słodka pomarańcza i amyris pobudzają i dodają energii.',
                        'Aromaterapia antystresowa – oczyszcza umysł, rozjaśnia myśli i redukuje zmęczenie, przywracając wewnętrzną harmonię.'
                    ],
                    'product_ids' => !empty($product_ids[2]) ? explode(',', $product_ids[2]) : [2],
                    'black_text' => true,
                    'link' => '/produkty/linie-serie/eliksir-zycia-perilla-lukrecja/',
                ],
            ];

            foreach ($cards as $index => $card) :
            ?>
                <div class="card" id="card-<?php echo $index; ?>">
                    <div class="dd-container">
                        <div class="card-header">
                            <div class="icon">
                                <?php get_template_part('templates/parts/cards-icon', get_post_format()); ?>
                                <h2 class="section-title <?php echo $card['black_text'] ? 'black_text' : ''; ?>"><?php echo esc_html($card['title']); ?></h2>
                            </div>
                            <p class="section-subtitle  <?php echo $card['black_text'] ? 'black_text' : ''; ?>"><?php echo esc_html($card['subtitle']); ?></p>
                            <a href="<?php echo home_url($card['link']); ?>" class="main-button  <?php echo $card['black_text'] ? 'black_text' : ''; ?>">Zobacz więcej</a>
                        </div>
                        <div class="card-content">
                            <div class="image">
                                <img src="<?php echo get_template_directory_uri() . esc_url($card['image']); ?>" alt="<?php echo esc_attr($card['title']); ?>" />
                            </div>
                            <div class="description <?php echo $card['black_text'] ? 'black_text' : ''; ?>">
                                <?php foreach ($card['description'] as $text) : ?>
                                    <p><?php echo esc_html($text); ?></p>
                                    <hr>
                                <?php endforeach; ?>
                            </div>
                            <div class="products">
                                <?php
                                $args = [
                                    'post_type'      => 'product',
                                    'posts_per_page' => 2,
                                    'orderby'        => 'date',
                                    'order'          => 'DESC',
                                    'post__in'       => array_slice($card['product_ids'], 0, 2),
                                ];

                                echo WooCommerceProductListing::generateListing($args);
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </section>

    <section class="section-movie">
        <div class="dd-container">
            <div class="wrapper">
                <div class="section-header">
                    <img height="120px" src="<?php echo get_template_directory_uri(); ?>/public/fito-spa-icon-movie.svg" alt="FITO SPA">
                    <p class="section-subtitle">Poznajmy się! Usłysz historię i misję naszej marki od
                        Współzałożycielki Anny i sprawdź, czy jesteśmy dla Ciebie.</p>
                </div>
                <div class="section-content">
                    <?php
                    $video = [
                        'id'    => 'vIHD2OxmRGY',
                        'title' => 'Zainspiruj się i stwórz swoje domowe SPA',
                    ];

                    $embed_url = 'https://www.youtube.com/embed/' . esc_attr($video['id']) . '?rel=0&modestbranding=1';
                    ?>
                    <div class="movie">
                        <div class="video-container">
                            <iframe class="responsive-iframe" src="<?php echo esc_url($embed_url); ?>"
                                title="<?php echo esc_attr($video['title']); ?>" frameborder="0"
                                loading="lazy"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                allowfullscreen>
                            </iframe>
                        </div>
                    </div>
                    <div class="movie-info">
                        <div class="insider">
                            <p class="movie-text">100% NATURALNE</p>
                            <hr>
                            <p class="movie-text">CZYSTOŚĆ I JAKOŚĆ PRODUKTÓW</p>
                            <hr>
                            <p class="movie-text">WYSOKIE STĘŻENIE SKŁADNIKÓW AKTYWNYCH</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <?php get_template_part( 'templates/view/template-partnerzy', get_post_format() ); ?>
    <section class="section-find-us">
        <div class="dd-container">
            <div class="wrapper">
                <div class="insider">
                    <h2 class="section-title">Nasze produkty znajdziesz w najlepszych SPA w całym kraju</h2>
                    <p class="section-subtitle">Sprawdź, gdzie możesz się wybrać na wyjątkowe rytuały i masaże by rozpieścić swoje zmysły!</p>
                    <div class="buttons">
                        <a href="<?php echo esc_url(home_url('lokalizator-spa'));?>" class="main-button primary">Lokalizator spa</a>
                        <a href="https://pro.fitospa.pl/rejestracja" class="main-button reverse">Nawiąż współpracę profesjonalną</a>
                    </div>
                    
                </div>
                <div class="image">
                    <img src="<?php echo get_template_directory_uri(); ?>/public/relaxing-spa-salon.webp" alt="Relaksujący salon spa" />
                </div>
  
            </div>
        </div>
    </section>

    <section class="section-more-inspiration">
        <div class="wrapper">   
            <h2 class="section-title">Więcej inspiracji domowego spa</h2>
            <div class="swiper-button-container">
                <div class="more-inspired swiper-button-next"></div>
                <div class="more-inspired swiper-button-prev"></div>
            </div>
        </div>
        <div class="dd-container">
            <div class="swiper swiperMoreInspiration">
                <div class="swiper-wrapper">
                    <?php
                    $videos = [
                        ['id' => 'XDtrty406JA', 'title' => 'MASAŻ ANTYCELLULITOWY I WYSZCZUPLAJĄCY', 'date' => '2022-12-07', 'url' => 'https://www.youtube.com/watch?v=XDtrty406JA', 'duration' => '7:14'],
                        ['id' => 'UTP-746Xm2k', 'title' => 'JAK OLEJOWAĆ WŁOSY?', 'date' => '2022-11-07', 'url' => 'https://www.youtube.com/watch?v=UTP-746Xm2k', 'duration' => '9:46'],
                        ['id' => 'ug6hAgKm3jw', 'title' => 'ODMŁADZAJĄCY MASAŻ TWARZY', 'date' => '2022-12-17', 'url' => 'https://www.youtube.com/watch?v=ug6hAgKm3jw', 'duration' => '9:16']
                    ];

                    foreach ($videos as $video) :
                        $thumbnail_url = 'https://img.youtube.com/vi/' . esc_attr($video['id']) . '/hqdefault.jpg'; ?>
                        <div class="swiper-slide">
                            <div class="post-thumbnail">
                                <a href="<?php echo esc_url($video['url']); ?>" target="_blank">
                                    <img src="<?php echo esc_url($thumbnail_url); ?>" alt="<?php echo esc_attr($video['title']); ?>">
                                    <span class="video-duration"><?php echo esc_html($video['duration']); ?></span>
                                </a>
                            </div>
                            <a href="<?php echo esc_url($video['url']); ?>" class="post-link">
                                <div class="post-info">
                                    <div class="left">
                                        <h3 class="post-title"><?php echo esc_html($video['title']); ?></h3>
                                        <span class="post-date"><?php echo esc_html($video['date']); ?></span>
                                    </div>
                                    <div class="button">
                                        <?php get_template_part('templates/parts/category-button', get_post_format()); ?>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </section>

    <section class="wpisy-section dd-container">
        <h2 class="section-title">BLOG</h2>
        <div class="blog-posts">
            <?php
        $args = array(
            'posts_per_page' => 4,
            'post_type' => 'post',
        );

        $query = new WP_Query($args);

        if ($query->have_posts()) :
            while ($query->have_posts()) : $query->the_post(); ?>
            <div class="blog-post">
                <div class="post-thumbnail">
                    <a href="<?php the_permalink(); ?>">
                        <?php if (has_post_thumbnail()) : ?>
                        <?php the_post_thumbnail('medium');  ?>
                        <?php endif; ?>
                    </a>
                </div>
                <div class="post-info">
                    <h3>
                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                    </h3>
                    <span class="post-date">
                        <?php echo get_the_date(); ?>
                    </span>
                </div>
            </div>
            <?php endwhile;
            wp_reset_postdata();
        else : ?>
            <p>Brak wpisów do wyświetlenia.</p>
            <?php endif; ?>
        </div>
        <div class="button-nowosci">
            <a href="<?php echo esc_url(home_url('zdrowaskora-blog'));?>"><button
                    class="dd-black-border-button">więcej</button></a>
        </div>
    </section>
</main>

<?php get_footer(); ?>

<script>
document.addEventListener("DOMContentLoaded", () => {
    const swiper = new Swiper(".mainSwiper", {
        effect: "fade",
        loop: true,
        pagination: {
            el: ".swiper-pagination",
        },
        autoplay: {
            delay: 4000,
        },
    });

    swiper.emit("slideChange");

     // Bestsellers Swiper
    if (document.querySelector(".swiperBestsellers")) {
        new Swiper(".swiperBestsellers", {
            slidesPerView: 1,
            spaceBetween: 20,
            loop: true,
            navigation: {
                nextEl: ".bestsellers.swiper-button-next",
                prevEl: ".bestsellers.swiper-button-prev",
            },
            breakpoints: {
                1024: { slidesPerView: 4 },
            },
            autoplay: {
                delay: 5000,
            },
        });
    }

     // Inspired Swiper
    if (document.querySelector(".swiperInspired")) {
        new Swiper(".swiperInspired", {
            slidesPerView: 1,
            spaceBetween: 10,
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
            breakpoints: {
                1199.98: { slidesPerView: 2 },
            },
            autoplay: {
                delay: 5000,
            },
        });
    }

    // Inspired Posts Swiper
    if (document.querySelector(".swiperInspiredPosts")) {
        new Swiper(".swiperInspiredPosts", {
            slidesPerView: 1.5,
            spaceBetween: 30,
            navigation: {
                nextEl: ".inspired.swiper-button-next",
                prevEl: ".inspired.swiper-button-prev",
            },
            breakpoints: {
                768: { slidesPerView: 2.5 },
                1024: { slidesPerView: 4.5 },
            },
            autoplay: {
                delay: 5000,
            },
        });
    }

    // More Inspired Posts Swiper
    if (document.querySelector(".swiperMoreInspiration")) {
        new Swiper(".swiperMoreInspiration", {
            slidesPerView: 1,
            spaceBetween: 30,
            navigation: {
                nextEl: ".more-inspired.swiper-button-next",
                prevEl: ".more-inspired.swiper-button-prev",
            },
            breakpoints: {
                1024: { slidesPerView: 3},
            },
            autoplay: {
                delay: 5000,
            },
        });
    }
});

document.addEventListener("DOMContentLoaded", function () {
    document.querySelectorAll(".swiperFor").forEach(function (swiperElement) {
        const swiperId = swiperElement.classList[1];

        new Swiper(swiperElement, {
            slidesPerView: 1,
            spaceBetween: 20,
            loop: true,
            navigation: {
                nextEl: `.swiper-button-next[data-swiper-id="${swiperId.split("-").pop()}"]`,
                prevEl: `.swiper-button-prev[data-swiper-id="${swiperId.split("-").pop()}"]`,
            },
            breakpoints: {
                1024: { slidesPerView: 3 },
            },
            autoplay: {
                delay: 5000,
            },
        });
    });
});

</script>