<?php

/*
Template name: Blog
*/

get_header(); ?>

<main>
    <div class="blog-section dd-container">
        <?php dd_generate_breadcrumbs() ?>
        <h1 class="page-title">#zdrowaskóra blog</h1>
        <div class="posts-wrapper">
            <div class="blog-posts">
            <?php
            // Używamy standardowej pętli WordPress - hook pre_get_posts modyfikuje główne zapytanie
            if ( have_posts() ) :
                while ( have_posts() ) : the_post();
                    ?>

                    <div class="blog-post">
                        <div class="post-thumbnail">
                            <a href="<?php the_permalink(); ?>">
                                <?php the_post_thumbnail('full'); ?>
                            </a>
                        </div>
                        <div class="post-content">
                            <h3 class="post-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
                            <div class="meta">
                                <p class="post-date"><?php echo get_the_date(); ?></p>
                                <a class="read-more" href="<?php the_permalink(); ?>">czytaj więcej</a>
                            </div>
                        </div>
                    </div>

                    <?php
                endwhile;
                ?>
                </div>
            </div>

            <!-- Informacja o liczbie wyświetlanych wpisów oraz paginacja -->
            <div class="posts-navigation">
                <div class="pagination">
                    <?php
                    // Standardowa paginacja WordPress - działa automatycznie z głównym zapytaniem
                    echo paginate_links(array(
                        'prev_text' => '<i class="fa fa-chevron-left"></i>',
                        'next_text' => '<i class="fa fa-chevron-right"></i>',
                        'before_page_number' => '<span class="page-num">',
                        'after_page_number' => '</span>',
                    ));
                    ?>
                </div>
            </div>

            <?php
            else :
                echo '<p>Brak wpisów do wyświetlenia.</p>';
            endif;
            ?>

    </div>
</div>


<section class="laka">
    <h2>FITOSPA ZOBOWIĄZUJE SIĘ DO JAKOŚCI BEZ KOMPROMISÓW</h2>
    <p>Kosmetyki FITO SPA są w 100% naturalne. Dbamy o czystość i jakość naszych produktów oraz bardzo wysokie stężenie składników aktywnych.<br>Wykorzystujemy oleje z roślin o najszerszym prozdrowotnym spektrum działania.</p>
  </section>
  <section class="nowosci-section woocommerce">
    <div class="naglowek-nowosci">
      <h2>SPRAWDŹ<br>nasze nowości</h2>
    </div>
    <div class="dd-container">
        <ul class="products columns-4">
            <?php
            $args = array(
                'limit' => 8,
                'orderby' => 'date',
                'order' => 'DESC',
                'return' => 'objects'
            );

            $recent_products = wc_get_products($args);

            if (!empty($recent_products)) {
                foreach ($recent_products as $product) {
                    ?>
                    <div <?php wc_product_class( '', $product ); ?>>
                        <div class="product-item">
                            <a href="<?php echo get_permalink($product->get_id()); ?>" class="product-link">
                                <div class="product-image">
                                    <?php echo $product->get_image(); ?>
                                </div>
                                <h3 class="product-name"><?php echo $product->get_name(); ?></h3>
                                <span class="price"><?php echo $product->get_price_html(); ?></span>
                            </a>
                            <div class="product-variations">
                                <?php dd_product_variations($product); ?>
                                <?php dd_display_add_to_cart_button($product); ?>
                            </div>
                        </div>
                    </div>

                    <?php
                }
            } else {
                echo '<p>Brak nowych produktów.</p>';
            }
            ?>
        </ul>
    </div>
    <div class="button-nowosci">
    <a href="/sklep"><button class="dd-black-border-button">więcej</button></a>
    </div>
  </section>
</main>

<?php get_footer(); ?>