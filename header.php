<?php
use DD\FitospaTheme\HeaderMiniCart;
?>

<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="profile" href="http://gmpg.org/xfn/11">
    <title><?php wp_title('-', true, 'right'); ?></title>
     <?php if (function_exists('the_custom_logo')) {
        $custom_logo_id = get_theme_mod('custom_logo');
        if ($custom_logo_id) {
            $logo_url = wp_get_attachment_image_src($custom_logo_id, 'full');
            echo '<link rel="icon" href="' . esc_url($logo_url[0]) . '" type="image/x-icon">';
        }
    } ?>
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>

    <!-- Preload critical fonts and images -->
    <link rel="preload" href="<?php echo get_template_directory_uri(); ?>/assets/fonts/Lato-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="<?php echo get_template_directory_uri(); ?>/assets/fonts/Lato-Light.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="<?php echo get_template_directory_uri(); ?>/assets/fonts/FreightBigProLight-Regular.woff2" as="font" type="font/woff2" crossorigin>

    <?php wp_head(); ?>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css"/>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
</head>
<body <?php body_class(); ?>>
<?php if ( function_exists( 'gtm4wp_the_gtm_tag' ) ) { gtm4wp_the_gtm_tag(); } ?>
<header>
    <div class="desktop-header">
        <div class="header-block1">
            <div class="header-block1-content dd-container">
                <a href="https://pro.fitospa.pl/rejestracja/"><p>Dla profesjonalistów</p></a>
                <i class="fa-solid fa-circle"></i>
                <a href="<?php echo home_url('lokalizator-spa') ?>"><p>Lokalizator SPA</p></a>
                <span class="social-icon">
                    <a href="https://www.facebook.com/FitoSpaNatura" target="_blank" title="Facebook">
                        <i class="fa-brands fa-facebook" aria-hidden="true"></i>
                        <span class="sr-only">Facebook</span>
                    </a>
                    <a href="https://www.instagram.com/fitospa.pl/" target="_blank" title="Instagram">
                        <i class="fa-brands fa-instagram" aria-hidden="true"></i>
                        <span class="sr-only">Instagram</span>
                    </a>
                </span>
            </div>
        </div>

        <div class="header block2">
            <div class="header-block2-content dd-container">
                <nav class="nav-links dd-navbar-left">
                    <a href="<?php echo home_url('sklep') ?>">sklep</a>
                    <a href="<?php echo home_url('zdrowaskora-blog') ?>">blog</a>
                    <a href="<?php echo home_url('well-being') ?>">well being</a>
                    <a href="<?php echo home_url('o-nas') ?>">o nas</a>
                    <a href="<?php echo home_url('promocje') ?>/">promocje</a>
                </nav>

                <div class="logo dd-navbar-center">
                    <a href="<?php echo home_url() ?>"><img src="<?php echo get_template_directory_uri(); ?>/public/logo-sygnet.svg" alt="Logo" id="site-logo"></a>
                </div>

                <div class="right-side dd-navbar-right">
                    <div id="dd-navbar-search-box">
                        <form action="<?php echo home_url('/'); ?>" method="get" id="searchform" class="search-form">
                            <button type="submit" id="search-button">
                                <span class="search-icon" title="Wyszukaj" aria-hidden="true"><i class="fas fa-search"></i></span>
                                <span class="sr-only">Wyszukaj</span>
                            </button>
                            <input type="text" name="s" title="Wyszukaj produkt po tekście" id="search-input" placeholder="szukaj..." autocomplete="off">
                        </form>
                    </div>
                    <a href="javascript:void(0);" class="cart-trigger">
                        <img src="<?php echo get_template_directory_uri(); ?>/public/goods_6782661.svg" alt="Koszyk">
                        <div class="dd-header-cart-item">Koszyk (<?php echo WC()->cart->get_cart_contents_count(); ?>)</div>
                    </a>
                    <span class="konto">
                    <img src="<?php echo get_template_directory_uri(); ?>/public/user_7191311.svg" alt="Moje konto">
                    <a href="<?php echo home_url('moje-konto') ?>"> Moje konto</a>
                    </span>
                </div>

            <?php dd_generate_mini_cart('mini-cart'); ?>
        </div>
    </div>
    <div class="dd-header-block-category-menu">
        <div class="dd-container">
            <?php wp_nav_menu(
                array(
                    'theme_location' => 'dd-product-categories-menu',
                    'container_class' => ' dd-product-categories-menu'
                )
            ); ?>
        </div>
    </div>
</div>
</header>
<header class="mobile-header">
    <div class="mobile-site-header">
        <div class="mobile-header-content dd-container">
            <div class="mobile-logo">
                <a href="<?php echo home_url() ?>">
                    <img src="<?php echo get_template_directory_uri(); ?>/public/logo-sygnet.svg" alt="Logo" id="site-logo">
                </a>
            </div>
            <div class="cart-center dd-mobile-header-center-icons">
                <a href="javascript:void(0);" class="mobile-cart-trigger">
                    <img src="<?php echo get_template_directory_uri(); ?>/public/goods_6782661.svg" alt="Koszyk">
                </a>
                <a href="<?php echo home_url('moje-konto') ?>">
                    <img src="<?php echo get_template_directory_uri(); ?>/public/user_7191311.svg" alt="Moje konto">
                </a>
                <div class="menu-toggle">
                    <span class="menu-icon">☰</span>
                </div>
            </div>
        </div>
    </div>
    <nav id="mobile-menu" class="mobile-menu hidden">
        <?php
            wp_nav_menu( array(
                'theme_location' => 'dd-mobile',
                'menu_class'     => 'mobile-nav-menu',
                'container'      => 'ul',
                'link_after' => '<i class="fa-solid fa-chevron-down"></i>',
            ) );
        ?>
        <div id="dd-navbar-search-box">
            <form action="<?php echo home_url('/'); ?>" method="get" id="searchform" class="search-form">
                <button type="submit" id="search-button">
                    <span class="search-icon" title="Wyszukaj" aria-hidden="true"><i class="fas fa-search"></i></span>
                    <span class="sr-only">Wyszukaj</span>
                </button>
                <input type="text" name="s" title="Wyszukaj produkt po tekście" id="search-input" placeholder="szukaj..." autocomplete="off">
            </form>
        </div>
    </nav>
    <?php dd_generate_mini_cart('mobile-mini-cart'); ?>

</header>

<?php
    function dd_generate_mini_cart(string $id): void
    {
        ?>
        <div id="<?php echo $id; ?>" class="mini-cart" style="display:none;">
            <?php if ( ! WC()->cart->is_empty() ) : ?>
                <?php echo HeaderMiniCart::getMiniCartContent(); ?>
            <?php else : ?>
            <p><?php _e( 'Koszyk jest pusty.', 'woocommerce' ); ?></p>
            <?php endif; ?>
            </div>
            <!-- / Mini Cart Container -->
    <?php } ?>

    <?php
