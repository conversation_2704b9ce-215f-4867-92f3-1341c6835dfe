@font-face {
  font-family: "Lato";
  font-style: italic;
  font-weight: 100;
  font-display: swap;
  src: url(../fonts/Lato-HairlineItalic.woff2) format("woff2");
}
@font-face {
  font-family: "Lato";
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src: url(../fonts/Lato-LightItalic.woff2) format("woff2");
}
@font-face {
  font-family: "Lato";
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/Lato-Italic.woff2) format("woff2");
}
@font-face {
  font-family: "Lato";
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url(../fonts/Lato-BoldItalic.woff2) format("woff2");
}
@font-face {
  font-family: "Lato";
  font-style: italic;
  font-weight: 900;
  font-display: swap;
  src: url(../fonts/Lato-BlackItalic.woff2) format("woff2");
}
@font-face {
  font-family: "Lato";
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url(../fonts/Lato-Hairline.woff2) format("woff2");
}
@font-face {
  font-family: "Lato";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(../fonts/Lato-Light.woff2) format("woff2");
}
@font-face {
  font-family: "Lato";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/Lato-Regular.woff2) format("woff2");
}
@font-face {
  font-family: "Lato";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(../fonts/Lato-Bold.woff2) format("woff2");
}
@font-face {
  font-family: "Lato";
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url(../fonts/Lato-Black.woff2) format("woff2");
}
@font-face {
  font-family: "freight-big-pro-light";
  font-style: normal;
  font-weight: normal;
  font-display: swap;
  src: url(../fonts/FreightBigProLight-Regular.woff2) format("woff2");
}
:root {
  --font-base: Lato;
  --font-headings: freight-big-pro-light;
  --font-input: Lato;
}

body, #content, .entry-content, .post-content, .page-content, .post-excerpt, .entry-summary, .entry-excerpt, .widget-area, .widget, .sidebar, #sidebar, footer, .footer, #footer, .site-footer {
  font-family: "Lato";
}

#site-title, .site-title, #site-title a, .site-title a, .entry-title, .entry-title a, h1, h2, h3, h4, h5, h6, .widget-title, .elementor-heading-title {
  font-family: "Lato";
  font-weight: normal;
}

button, .button, input, select, textarea, .wp-block-button, .wp-block-button__link {
  font-family: "Lato";
}

.product_title {
  font-family: "Lato";
}

.woocommerce-product-details__short-description {
  font-family: "Lato";
}

.single-product .price {
  font-family: "freight-big-pro-light";
}

ul.products li.product .woocommerce-loop-product__title {
  font-family: "Lato";
}

ul.products li.product .price {
  font-family: "freight-big-pro-light";
}

ul.products li.product .button {
  font-family: "Lato";
}

.woocommerce .woocommerce-MyAccount-navigation {
  width: 20%;
}
@media screen and (max-width: 768px) {
  .woocommerce .woocommerce-MyAccount-navigation {
    width: 100%;
  }
}
.woocommerce .woocommerce-MyAccount-navigation ul {
  padding: 0;
}
.woocommerce .woocommerce-MyAccount-navigation ul li {
  display: flex;
  list-style: none !important;
  text-align: center;
  border: 1px solid #dfdfdf;
  height: fit-content !important;
}
.woocommerce .woocommerce-MyAccount-navigation ul li a {
  padding: 10px 0;
  width: 100%;
  text-decoration: none !important;
  color: #000000;
}
.woocommerce .woocommerce-MyAccount-navigation ul li.is-active {
  background: #4A6A65;
}
.woocommerce .woocommerce-MyAccount-navigation ul li.is-active a {
  color: #FFFFFF;
}
.woocommerce .woocommerce-MyAccount-content a {
  color: rgb(0, 115, 170);
  text-decoration: none;
}
.woocommerce .woocommerce-MyAccount-content .woocommerce-button {
  background: #4A6A65;
  color: #FFFFFF;
}
.woocommerce .woocommerce-MyAccount-content .woocommerce-orders-table__cell.woocommerce-orders-table__cell-order-actions {
  display: flex;
  gap: 10px;
  height: fit-content;
}
.woocommerce .unit-price {
  font-weight: 300;
}

.woocommerce-order-overview.woocommerce-thankyou-order-details.order_details {
  padding-left: 0;
}

.woocommerce-order-details a {
  color: rgb(0, 115, 170);
  text-decoration: none;
}

.product-tab-content, .tab-panel {
  overflow: hidden;
}

.dd-product-full-description {
  color: var(--text-gray);
  padding: 12px 25px;
}
.dd-product-full-description h1, .dd-product-full-description h2, .dd-product-full-description h3, .dd-product-full-description h4, .dd-product-full-description h5, .dd-product-full-description h6 {
  font-family: Lato, sans-serif !important;
  font-weight: 500;
  margin: 20px 0 8px;
}
.dd-product-full-description h1 {
  font-size: 20px;
}
.dd-product-full-description h2, .dd-product-full-description h3 {
  font-size: 18px;
}
.dd-product-full-description p {
  font-size: 18px !important;
  font-weight: 300;
  line-height: 28px;
  margin: 10px 0 15px;
}
.dd-product-full-description strong {
  font-weight: 500;
}
.dd-product-full-description a, .dd-product-full-description .entry-content a {
  text-decoration: none;
  color: var(--accent-red);
}
.dd-product-full-description a:hover {
  opacity: 0.7;
}

.woocommerce nav.woocommerce-pagination ul li span.current, .woocommerce nav.woocommerce-pagination ul li a:hover, .woocommerce nav.woocommerce-pagination ul li a:focus {
  background: #e9e6ed;
  color: #000000;
}

.dd-omnibus-message-wrapper {
  padding-bottom: 20px;
  font-weight: 300;
}

* {
  box-sizing: border-box;
}

.dd-container {
  margin: auto;
  max-width: 1440px;
  width: 100%;
  padding: 0 50px;
}

.desktop {
  display: block;
}

.mobile {
  display: none;
}

.page-content {
  padding-bottom: 30px;
}

.sr-only {
  border: 0 !important;
  clip: rect(1px, 1px, 1px, 1px) !important;
  -webkit-clip-path: inset(50%) !important;
  clip-path: inset(50%) !important;
  height: 1px !important;
  overflow: hidden !important;
  margin: -1px !important;
  padding: 0 !important;
  position: absolute !important;
  width: 1px !important;
  white-space: nowrap !important;
}

.black_text {
  color: #000000 !important;
}

.main-button.black_text {
  border-color: #000000 !important;
}

.products {
  display: grid;
  grid-template-rows: 1fr;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 15px;
}
.products::before {
  display: none !important;
}
.products .product {
  display: flex;
  flex-direction: column;
}
.products .product form.cart {
  margin: 0 !important;
}
.products .product .product-item {
  height: 100%;
  justify-content: space-between;
}
.products .product .product-item .product-link {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
.products .product .product-item .product-link .price {
  margin-top: auto;
  margin-bottom: 1rem;
}

.product-variations span {
  font-weight: 300;
  color: var(--text-gray);
}
.product-variations .variations {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}
.product-variations .variations .variation-button {
  background: none;
  color: inherit;
  border: 1px solid #dfdfdf;
  padding: 10px 38px;
  font: inherit;
  cursor: pointer;
  outline: inherit;
  font-family: "Lato", sans-serif;
  white-space: pre;
}
.product-variations .variations .variation-button.out-of-stock {
  background-color: #f4f4f4;
  color: #a1a1a1;
}
.product-variations .variations .variation-button:hover, .product-variations .variations .variation-button:focus, .product-variations .variations .variation-button:active, .product-variations .variations .variation-button.checked {
  border-color: #4A6A65;
  outline: 1px solid #4A6A65;
}

.single-product .product-details .price {
  margin-top: 50px;
}
.single-product .product-details .single_add_to_cart_button.disabled {
  display: none;
}
.single-product .product-details .product-variations {
  margin-top: 20px;
}

body:not(.single-product) .product .product-item {
  position: relative;
}
body:not(.single-product) .product .product-item .variations,
body:not(.single-product) .product .product-item .single_add_to_cart_button {
  visibility: hidden;
}
body:not(.single-product) .product .product-item.active .variations,
body:not(.single-product) .product .product-item.active .single_add_to_cart_button {
  visibility: visible;
}
body:not(.single-product) .product .product-item .product-variations {
  display: flex;
  flex-direction: column;
  position: relative;
  gap: 15px;
  width: 100%;
  min-height: 150px;
  font-family: "Lato", sans-serif;
}
body:not(.single-product) .product .product-item .product-variations .variations .variation-button {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #dfdfdf;
  padding: 8px 38px;
}
body:not(.single-product) .product .product-item .product-variations .variations .variation-button.out-of-stock {
  cursor: not-allowed;
}
body:not(.single-product) .product .product-item .product-variations .variations .variation-button.variation-1 {
  width: calc(100% - 10px);
}
body:not(.single-product) .product .product-item .product-variations .variations .variation-button.variation-2 {
  width: calc(50% - 8px);
}
body:not(.single-product) .product .product-item .product-variations .variations .variation-button.variation-3 {
  padding: 8px 16px;
  width: calc(33.33% - 10px);
}
body:not(.single-product) .product .product-item .product-variations .variations .variation-button.variation-4 {
  padding: 8px 16px;
  width: calc(50% - 8px);
}
body:not(.single-product) .product .product-item .product-variations .variations .variation-button.variation-5 {
  padding: 8px 16px;
  width: calc(33.33% - 10px);
}
body:not(.single-product) .product .product-item .product-variations .variations .variation-button.variation-6 {
  padding: 8px 16px;
  width: calc(33.33% - 10px);
}
body:not(.single-product) .product .product-item .product-variations .variations .variation-button:hover, body:not(.single-product) .product .product-item .product-variations .variations .variation-button:focus, body:not(.single-product) .product .product-item .product-variations .variations .variation-button:active, body:not(.single-product) .product .product-item .product-variations .variations .variation-button.checked {
  border-color: #4A6A65;
  outline: 1px solid #4A6A65;
}
body:not(.single-product) .product .product-item form.cart .quantity {
  display: none;
}
body:not(.single-product) .product .product-item form.cart .single_add_to_cart_button {
  margin: 0;
  padding: 10px 25px;
  width: 100%;
}
body:not(.single-product) .product .product-item form.cart .single_add_to_cart_button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.woocommerce ul.products li.product .price del,
.woocommerce ul.products li.product .price ins {
  height: 20px;
}

[disabled] {
  cursor: not-allowed;
}

.swiper-button-next,
.swiper-button-prev {
  background-size: 35%;
  background-position: center;
  background-repeat: no-repeat;
  border: 1px solid #4A6A65;
  border-radius: 50%;
  filter: brightness(0) saturate(100%);
  width: 45px !important;
  height: 45px !important;
  transition: 0.2s ease-out;
}
.swiper-button-next::after, .swiper-button-next::before,
.swiper-button-prev::after,
.swiper-button-prev::before {
  display: none !important;
}
.swiper-button-next:hover,
.swiper-button-prev:hover {
  background-color: rgba(105, 100, 100, 0.2862745098) !important;
  border: 1px solid transparent;
  transition: 0.2s ease-out;
  filter: unset;
}

.swiper-button-prev {
  background-image: url(../../public/arrow-left.svg) !important;
}

.swiper-button-next {
  background-image: url(../../public/arrow-right.svg) !important;
}

.swiper-wrapper ul {
  padding-inline-start: 0px;
}

.dd-product-tabs {
  margin-top: 20px;
  padding-top: 20px;
}
.dd-product-tabs .tabs {
  display: flex;
  border-bottom: 1px solid #d3d3d3;
}
.dd-product-tabs .tabs .tab {
  padding: 20px 65px;
  text-align: center;
  cursor: pointer;
  letter-spacing: 0.235px;
  color: #474747;
  font-weight: 400;
  text-transform: uppercase;
  outline: 2px solid transparent;
}
.dd-product-tabs .tabs .tab.active {
  font-weight: 600;
  border-bottom: 2px solid #787878;
  outline: 2px solid transparent;
}
.dd-product-tabs .dd-product-full-description .tab-panel {
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.dd-product-tabs .dd-product-full-description .tab-panel.active {
  display: block;
}
.dd-product-tabs .dd-product-full-description .tab-panel.fade-in {
  opacity: 1;
  transition: opacity 0.3s ease;
}

.dd-product-full-description img.alignright {
  float: right;
  margin-left: 20px;
  margin-bottom: 10px;
}
.dd-product-full-description img.alignleft {
  float: left;
  margin-right: 20px;
  margin-bottom: 10px;
}

.dd-products-opinions {
  padding-top: 50px;
  padding-bottom: 50px;
}

.categorie-section .woocommerce-products-header .term-description {
  font-size: 1rem;
}
.categorie-section .woocommerce-products-header .term-description p:not(.details p) {
  color: #615f59;
}
.categorie-section .woocommerce-products-header .term-description details summary {
  font-size: 1rem !important;
  color: #615f59;
}
.categorie-section .term-description {
  line-height: 1.5;
  font-weight: 300;
  padding: 20px 0 40px;
}
.categorie-section .term-description details summary {
  font-size: 1.25rem !important;
  color: #615f59 !important;
}
.categorie-section .term-description details p {
  font-size: 1rem;
}

.product-link:hover {
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}
.product-link:hover .dd-product-image:not(.has-second-image) {
  opacity: 1;
}
.product-link:hover .dd-product-image .dd-product-second-image {
  opacity: 1;
}
.product-link .dd-product-image {
  width: 100%;
}
.product-link .dd-product-image img {
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}
.product-link .dd-product-image .dd-product-second-image {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
}

.main-swipper {
  overflow: hidden;
}
.main-swipper picture, .main-swipper img {
  width: 100%;
  height: auto;
}

.swiper-pagination-bullet-active {
  background: #FFFFFF !important;
}

.section-category {
  padding-top: 3rem;
}
.section-category .wrapper {
  display: flex;
  align-items: center;
  font-style: normal;
  font-size: 1.125rem;
  line-height: 155%;
  gap: 50px;
  padding-bottom: 3rem;
}
.section-category .wrapper .section-subtitle {
  text-transform: uppercase;
  font-family: "Lato", sans-serif;
  font-weight: 400;
}
.section-category .wrapper hr {
  width: 100px;
  margin: 0;
  height: 0;
}
.section-category .wrapper .subtitle-text {
  color: #4d4c46;
}
.section-category .grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  grid-column-gap: 40px;
  grid-row-gap: 50px;
}
.section-category .grid .category-item {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 170px;
}
.section-category .grid .category-item:not(:last-child)::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1;
}
.section-category .grid .category-item:last-of-type {
  border: 1px solid #000000;
  min-height: 170px;
}
.section-category .grid .category-item:last-of-type .category-title {
  text-transform: capitalize;
}
.section-category .grid .category-item:last-of-type .category-link {
  border: none;
  color: #000000;
}
.section-category .grid .category-item:last-of-type .category-link:hover {
  background-color: unset;
}
.section-category .grid .category-item .category-link {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #FFFFFF;
  border: 1px solid #FFFFFF;
  transition: 0.2s ease-out;
  text-decoration: none;
}
.section-category .grid .category-item .category-link:hover svg {
  fill: rgba(255, 255, 255, 0.1294117647);
  transition: 0.2s ease-out;
}
.section-category .grid .category-item .category-link .category-image {
  position: relative;
  max-width: 100%;
  width: 100%;
  height: auto;
  max-height: 170px;
  object-fit: cover;
  aspect-ratio: 1/1;
}
.section-category .grid .category-item .category-link .category-content {
  position: absolute;
  bottom: 0;
  width: 100%;
  z-index: 2;
}
.section-category .grid .category-item .category-link .category-content .category-title {
  color: #FFFFFF;
}
.section-category .grid .category-item .category-link .category-content hr {
  width: 40px;
  margin: 0;
}
.section-category .grid .category-item .category-link .category-content .insider {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
}
.section-category .grid .category-item .category-link .category-title {
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 1rem;
  line-height: 150%;
  text-transform: uppercase;
}

.section-bestseller {
  padding: 5rem 0;
  overflow: hidden;
}
.section-bestseller .section-title {
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 2.5rem;
  line-height: 130%;
  text-align: center;
  color: #000000;
}
.section-bestseller .swiper-button-container {
  display: none;
}

.section-gray {
  background-color: rgba(169, 171, 172, 0.8);
  padding: 50px 0;
}
.section-gray .inner {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.section-gray .inner .inner-text,
.section-gray .inner .icon {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-basis: calc(20% - 5px);
}
.section-gray .inner .icon {
  max-width: 100px;
}
.section-gray .inner .inner-text {
  gap: 5px;
}
.section-gray .inner .inner-text ul {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 0;
  margin-bottom: 10px;
  padding: 0;
}
.section-gray .inner .inner-text ul li {
  text-align: center;
  list-style-type: none;
  font-weight: 300;
}
.section-gray .inner .inner-text .inner-title {
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 2rem;
  color: #000000;
}
.section-gray .inner .inner-text:nth-child(1) {
  flex-basis: fit-content;
}
@media (max-width: 768px) {
  .section-gray .inner {
    flex-wrap: wrap;
    gap: 20px;
  }
  .section-gray .inner .inner-text,
  .section-gray .inner .icon {
    flex-basis: 33%;
    max-width: 100px;
  }
}

.section-for {
  padding: 5rem 0;
}
.section-for .category-section {
  display: flex;
  justify-content: space-between;
  padding-bottom: 5rem;
}
.section-for .category-section .category-image {
  position: relative;
  display: flex;
  flex-basis: calc(30% - 25px);
}
.section-for .category-section .category-image img {
  max-width: 100%;
  width: 100%;
  height: auto;
  object-fit: cover;
}
.section-for .category-section .category-image ul {
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: absolute;
  top: 25px;
  left: 25px;
  padding: 0;
  margin: 0;
}
.section-for .category-section .category-image ul li {
  list-style-type: none;
  text-transform: uppercase;
  color: #FFFFFF;
  font-size: 1rem;
}
.section-for .category-section .category-content {
  flex-basis: calc(70% - 25px);
  min-width: calc(70% - 25px);
}
.section-for .category-section .category-content .category-text {
  display: flex;
  justify-content: space-between;
  font-style: normal;
  font-size: 3rem;
  line-height: 110%;
  letter-spacing: -0.01em;
}
.section-for .category-section .category-content .category-text .main-button {
  padding: 0 25px;
  width: fit-content;
  height: fit-content;
}
.section-for .category-section .category-content .category-text .main-button.reverse {
  color: #000000;
  border: 1px solid #000000;
  transition: 0.2s ease-out;
}
.section-for .category-section .category-content .category-text .main-button:hover {
  background-color: rgba(105, 100, 100, 0.2862745098);
  color: #000000;
  transition: 0.2s ease-out;
}
.section-for .category-section .category-content .category-text .category-title {
  font-size: 3rem;
  font-family: "freight-big-pro-light", sans-serif;
  font-weight: 400;
  margin: 0 0 20px 0;
}
.section-for .category-section .category-content .swiperFor .swiper-wrapper .swiper-slide {
  flex-shrink: 1;
}
.section-for .category-section .category-content .swiperFor .swiper-wrapper .swiper-slide .product .product-item {
  padding: 0;
}
.section-for .category-section .category-content .swiperFor .swiper-wrapper .swiper-slide .product .product-item .product-variations {
  min-height: 90px;
}

.section-quality {
  background-color: #f8f8f8;
  padding: 3rem 0;
}
.section-quality .wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 30px;
}
.section-quality .wrapper .insider {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 30px;
}
.section-quality .wrapper .insider .column {
  display: flex;
  align-items: center;
}
.section-quality .wrapper .insider .column .content {
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 15px;
  width: 100%;
}
.section-quality .wrapper .insider .column .content .icon {
  max-width: 160px;
}
.section-quality .wrapper .insider .column .content .text {
  font-family: "freight-big-pro-light", sans-serif;
  font-style: normal;
  text-align: right;
  font-weight: 400;
  font-size: 1.5rem;
  line-height: 130%;
  color: #000000;
  white-space: pre;
}
.section-quality .wrapper .insider .column .section-title {
  font-family: "freight-big-pro-light", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 3rem;
  line-height: 72px;
  letter-spacing: -0.01em;
  color: #000000;
}
.section-quality .wrapper .bottom-text {
  width: 100%;
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 110%;
  letter-spacing: 0.16em;
  text-transform: uppercase;
  color: #4d4c46;
}

.opinie-section {
  padding: 70px 0;
}
.opinie-section .section-title {
  text-align: center;
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 2.5rem;
  line-height: 130%;
  color: #000000;
  margin-bottom: 40px;
}

.section-inspired {
  padding-bottom: 2rem;
}
.section-inspired .inspired-wrapper {
  display: flex;
  gap: 50px;
  overflow: hidden;
}
.section-inspired .inspired-wrapper .inspired-image {
  flex-basis: calc(65% - 25px);
  position: relative;
}
.section-inspired .inspired-wrapper .inspired-image .section-title {
  margin-top: 0;
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 2.5rem;
  line-height: 130%;
  color: #000000;
}
.section-inspired .inspired-wrapper .inspired-image .video-container {
  min-height: 500px;
}
.section-inspired .inspired-wrapper .swiperInspired {
  display: flex;
  flex-direction: column;
  max-width: calc(35% - 25px);
}
.section-inspired .inspired-wrapper .swiperInspired .section-subtitle {
  font-family: "Lato", sans-serif;
  font-style: normal;
  text-align: center;
  font-weight: 400;
  font-size: 1.75rem;
  line-height: 130%;
  letter-spacing: -0.02em;
}
.section-inspired .inspired-wrapper .swiperInspired .swiper-button-container {
  position: relative;
  top: 30px;
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 15px 0;
}
.section-inspired .inspired-wrapper .swiperInspired .swiper-button-container .swiper-button-next {
  right: 35%;
  left: auto;
}
.section-inspired .inspired-wrapper .swiperInspired .swiper-button-container .swiper-button-prev {
  right: auto;
  left: 35%;
}
.section-inspired .inspired-wrapper .swiperInspired .swiper-slide .product .product-item .product-link .dd-product-image img {
  height: fit-content !important;
}

.section-inspired-posts .dd-container {
  position: relative;
}
.section-inspired-posts .dd-container .section-title {
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 1.75rem;
  line-height: 130%;
  letter-spacing: -0.02em;
}
.section-inspired-posts .dd-container .swiper-button-container {
  position: absolute;
  top: 20px;
  right: 105px;
}
.section-inspired-posts .dd-container .swiper-button-container .swiper-button-next {
  left: var(--swiper-navigation-sides-offset, 5px);
  right: auto;
}
.section-inspired-posts .dd-container .swiper-button-container .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 20px);
  left: auto;
}
.section-inspired-posts .dd-container .swiperInspiredPosts .swiper-wrapper .swiper-slide .post-thumbnail img {
  max-height: 155px;
}

.section-cards {
  padding: 6rem 0;
  overflow: hidden;
}
.section-cards .wrapper {
  display: flex;
  flex-direction: column;
}
.section-cards .wrapper .card {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-height: 170px;
  height: auto;
  color: #FFFFFF;
  transition: max-height 1s ease-in-out;
  overflow: hidden;
  position: relative;
}
.section-cards .wrapper .card:nth-child(1) {
  background-color: #b24837;
  min-height: 180px;
}
.section-cards .wrapper .card:nth-child(2) {
  background-color: #4a6a65;
}
.section-cards .wrapper .card:nth-child(3) {
  background-color: #a9abac;
}
.section-cards .wrapper .card.open {
  max-height: 5000px;
  transition: max-height 1s ease-in-out;
}
.section-cards .wrapper .card .header-title {
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 1rem;
  line-height: 32px;
  text-transform: uppercase;
}
.section-cards .wrapper .card .card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 3rem 0;
  gap: 10px;
}
.section-cards .wrapper .card .card-header .icon {
  display: flex;
  align-items: center;
  gap: 25px;
  min-width: 510px;
  cursor: pointer;
}
.section-cards .wrapper .card .card-header .icon svg {
  transition: 0.2s ease-out;
}
.section-cards .wrapper .card .card-header .icon svg:hover {
  transition: 0.2s ease-out;
  fill: rgba(255, 255, 255, 0.1294117647);
}
.section-cards .wrapper .card .card-header .icon .section-title {
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 36px;
  line-height: 110%;
  margin: 0;
  letter-spacing: -0.01em;
}
.section-cards .wrapper .card .card-header .section-subtitle {
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 18px;
  line-height: 24px;
  max-width: 350px;
}
.section-cards .wrapper .card .card-header .main-button {
  padding: 15px 35px;
  width: fit-content;
  transition: 0.2s ease-out;
}
.section-cards .wrapper .card .card-header .main-button:hover {
  border: 1px solid #FFFFFF;
  color: #FFFFFF;
  transition: 0.2s ease-out;
  background-color: rgba(255, 255, 255, 0.1294117647);
}
.section-cards .wrapper .card hr {
  margin: 10px 0;
  border: 0;
  border-top: 1px solid #fff;
}
.section-cards .wrapper .card .black_text hr {
  border-color: #000000 !important;
}
.section-cards .wrapper .card .card-content {
  display: flex;
  justify-content: space-between;
  gap: 50px;
  padding: 3rem 0;
}
.section-cards .wrapper .card .card-content .image {
  flex-basis: calc(25% - 25px);
}
.section-cards .wrapper .card .card-content .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.section-cards .wrapper .card .card-content .description {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex-basis: calc(30% - 25px);
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 1.125rem;
  line-height: 24px;
}
.section-cards .wrapper .card .card-content .products {
  display: flex;
  flex: 1 1 0;
  justify-content: space-between;
  gap: 30px;
}
.section-cards .wrapper .card .card-content .products .product-item {
  min-width: 50%;
}
.section-cards .wrapper .card .card-content .products .product-item::after {
  display: none;
}
.section-cards .wrapper .card .card-content .products .product-item .product {
  background-color: #FFFFFF;
  padding: 0 1rem 1rem 1rem;
  height: 100%;
}
.section-cards .wrapper .card .card-content .products .product-item .product .product-item {
  height: fit-content;
  justify-content: flex-start;
}
.section-cards .wrapper .card .card-content .products .product-item .product .product-item .product-link .price {
  margin-top: 0;
}
.section-cards .wrapper .card .card-content .products .product-item .product .product-item .product-variations {
  min-height: fit-content;
}
.section-cards .wrapper .card .card-content .products .product-item .product .product-item .product-variations .variations .variation-button {
  color: #000000;
}

.section-movie .wrapper {
  display: flex;
  flex-direction: column;
  gap: 25px;
}
.section-movie .wrapper .section-header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.section-movie .wrapper .section-header .section-title {
  font-family: "Lato", sans-serif;
  text-transform: uppercase;
  font-style: normal;
  margin: 0;
  font-weight: 400;
  font-size: 3.75rem;
  line-height: 72px;
  color: #000000;
}
.section-movie .wrapper .section-header .section-subtitle {
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 1.125rem;
  line-height: 155%;
  color: #4d4c46;
  max-width: 35%;
  text-align: center;
}
.section-movie .wrapper .section-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.section-movie .wrapper .section-content .movie {
  display: flex;
  flex: 1 1 75%;
}
.section-movie .wrapper .section-content .movie .video-container {
  min-height: 590px;
}
.section-movie .wrapper .section-content .movie-info {
  flex: 1 1 25%;
  display: flex;
  background-color: rgba(169, 171, 172, 0.8);
  border: 1px solid rgba(169, 171, 172, 0.8);
  min-height: 590px;
}
.section-movie .wrapper .section-content .movie-info .insider {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 3rem;
  gap: 15px;
}
.section-movie .wrapper .section-content .movie-info hr {
  width: 100%;
  border: 0;
  border-top: 1px solid #000000;
  margin: 0.5rem 0;
}

.home .partnerzy {
  padding: 3rem 0;
  border: 0;
}

.section-find-us {
  display: flex;
  background-color: #f8f8f8;
  overflow: hidden;
}
.section-find-us .wrapper {
  display: flex;
  justify-content: space-between;
}
.section-find-us .wrapper .insider {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-basis: 50%;
  padding: 2rem 4rem 0 0rem;
  gap: 25px;
}
.section-find-us .wrapper .insider .section-title {
  font-family: "freight-big-pro-light", sans-serif;
  font-style: normal;
  margin: 0;
  font-weight: 400;
  font-size: 3.75rem;
  line-height: 72px;
  letter-spacing: -0.01em;
}
.section-find-us .wrapper .insider .section-subtitle {
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 1rem;
  line-height: 150%;
  color: #4d4c46;
}
.section-find-us .wrapper .buttons {
  display: flex;
  gap: 20px;
}
.section-find-us .wrapper .buttons .main-button {
  padding: 15px 25px;
  width: calc(55% - 10px);
  height: fit-content;
  text-align: center;
}
.section-find-us .wrapper .buttons .main-button.primary {
  background: #4A6A65;
  color: #FFFFFF;
  transition: 0.2s ease-out;
  border: 1px solid #4A6A65;
}
.section-find-us .wrapper .buttons .main-button.primary:hover {
  background: rgba(74, 106, 101, 0.8);
  border: 1px solid rgba(74, 106, 101, 0.8);
}
.section-find-us .wrapper .buttons .main-button.reverse {
  color: #000000;
  border: 1px solid #000000;
  transition: 0.2s ease-out;
}
.section-find-us .wrapper .buttons .main-button.reverse:hover {
  background-color: rgba(105, 100, 100, 0.2862745098);
  color: #000000;
  transition: 0.2s ease-out;
}
.section-find-us .image {
  margin-right: -100vw;
  max-width: 100vw;
  position: relative;
  right: 48%;
  width: 100vw;
  display: flex;
}
.section-find-us .image img {
  height: 550px;
  max-width: 100%;
  width: 50%;
  object-fit: cover;
}

.section-more-inspiration {
  padding: 3rem 0;
}
.section-more-inspiration .wrapper .section-title {
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 2.5rem;
  line-height: 130%;
  text-align: center;
  margin-bottom: 3rem;
}
.section-more-inspiration .swiper-slide {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.section-more-inspiration .swiper-slide .post-thumbnail {
  position: relative;
}
.section-more-inspiration .swiper-slide .post-thumbnail img {
  max-height: 230px;
  object-fit: cover;
  object-position: center;
}
.section-more-inspiration .swiper-slide .post-thumbnail .video-duration {
  position: absolute;
  bottom: 0px;
  right: 5px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 14px;
  font-weight: bold;
  padding: 3px 6px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  line-height: 1;
}
.section-more-inspiration .swiper-slide .post-link {
  text-decoration: none;
}
.section-more-inspiration .swiper-slide .post-link svg {
  filter: brightness(0) saturate(100%);
  transition: 0.2s ease-out;
}
.section-more-inspiration .swiper-slide .post-link svg:hover {
  fill: rgba(105, 100, 100, 0.2862745098);
  transition: 0.2s ease-out;
  filter: unset;
}
.section-more-inspiration .swiper-slide .post-link .post-info {
  display: flex;
  flex-direction: row;
  gap: 35px;
}
.section-more-inspiration .swiper-slide .post-link .post-info .left {
  display: flex;
  flex-direction: column;
  flex: 1 1 0;
}
.section-more-inspiration .swiper-slide .post-link .post-info .left .post-title {
  font-family: "Lato", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 1.125rem;
  line-height: 155%;
  text-transform: uppercase;
  letter-spacing: normal;
  color: #000000;
}
.section-more-inspiration .swiper-slide .post-link .post-info .left .post-date {
  color: #000000;
}
.section-more-inspiration .swiper-slide .post-link .post-info .button {
  margin: 10px 0;
}

.section-inspired .video-container,
.section-movie .video-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  padding-top: 56.25%;
}
.section-inspired .video-container .responsive-iframe,
.section-movie .video-container .responsive-iframe {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
}
.section-inspired .video-container .video-placeholder,
.section-movie .video-container .video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  z-index: 2;
  cursor: pointer;
}
.section-inspired .video-container .video-placeholder img,
.section-movie .video-container .video-placeholder img {
  width: 100%;
  min-height: 535px;
  height: 100%;
  object-fit: cover;
  object-position: top left;
}

.section-bestseller .swiper-wrapper,
.section-inspired .swiper-wrapper,
.section-for .swiper-wrapper {
  position: relative;
}
.section-bestseller .swiper-wrapper .swiper-slide,
.section-inspired .swiper-wrapper .swiper-slide,
.section-for .swiper-wrapper .swiper-slide {
  height: auto;
}
.section-bestseller .swiper-wrapper .swiper-slide .product,
.section-inspired .swiper-wrapper .swiper-slide .product,
.section-for .swiper-wrapper .swiper-slide .product {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.section-bestseller .swiper-wrapper .swiper-slide .product .product-item,
.section-inspired .swiper-wrapper .swiper-slide .product .product-item,
.section-for .swiper-wrapper .swiper-slide .product .product-item {
  padding-top: 4rem;
  height: 100%;
  width: 100%;
}
.section-bestseller .swiper-wrapper .swiper-slide .product .product-item .product-link,
.section-inspired .swiper-wrapper .swiper-slide .product .product-item .product-link,
.section-for .swiper-wrapper .swiper-slide .product .product-item .product-link {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.section-bestseller .swiper-wrapper .swiper-slide .product .product-item .price,
.section-inspired .swiper-wrapper .swiper-slide .product .product-item .price,
.section-for .swiper-wrapper .swiper-slide .product .product-item .price {
  margin-top: auto;
  margin-bottom: 1rem;
}

.wpisy-section {
  padding-bottom: 3rem;
}

@media (max-width: 1199.98px) {
  .section-quality .wrapper .insider {
    flex-wrap: wrap;
  }
  .section-gray .left-right {
    flex-direction: column;
  }
  .opinie-section .ti-widget.ti-goog .ti-reviews-container .ti-controls {
    display: flex !important;
  }
  .opinie-section .ti-widget.ti-goog .ti-reviews-container .ti-controls .ti-next {
    right: 42% !important;
  }
  .opinie-section .ti-widget.ti-goog .ti-reviews-container .ti-controls .ti-prev {
    left: 42% !important;
  }
  .section-inspired .inspired-wrapper {
    flex-direction: column;
    gap: 35px;
  }
  .section-inspired .inspired-wrapper .inspired-image {
    flex-basis: 100%;
  }
  .section-inspired .inspired-wrapper .inspired-image img {
    display: flex;
  }
  .section-inspired .inspired-wrapper .inspired-image .section-title {
    font-size: 2rem;
  }
  .section-inspired .inspired-wrapper .swiperInspired {
    max-width: 100%;
  }
  .section-inspired .inspired-wrapper .swiperInspired .section-subtitle {
    font-size: 1.375rem;
  }
  .section-cards .wrapper .card .card-header .icon {
    min-width: unset;
  }
  .section-cards .wrapper .card .card-header .main-button {
    text-align: center;
    padding: 15px;
  }
  .section-find-us .wrapper {
    flex-direction: column;
    gap: 35px;
  }
  .section-find-us .wrapper .insider .buttons {
    flex-wrap: wrap;
  }
  .section-find-us .wrapper .insider .buttons .main-button {
    width: 100%;
  }
  .section-find-us .wrapper .image {
    max-width: 100vw;
    position: relative;
    right: 8%;
    min-width: 115vw;
  }
  .section-find-us .wrapper .image img {
    height: 375px;
    max-width: 100%;
    width: 100%;
    object-fit: cover;
  }
}
@media (max-width: 991.98px) {
  .section-category {
    padding-top: 5rem;
  }
  .section-category .wrapper {
    align-items: center;
    gap: 25px;
    padding-bottom: 2rem;
  }
  .section-category .wrapper hr {
    border: 1px solid black;
    width: 100%;
  }
  .section-category .wrapper .section-subtitle {
    margin: 5px 0 0 0;
  }
  .section-category .subtitle-text {
    padding-bottom: 2rem;
  }
  .section-gray .left-right .inner-left .dd-black-border-button {
    padding: 15px;
  }
  .section-gray .left-right .inner-right {
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 30px;
  }
  .section-gray .left-right .inner-right .inner-text,
  .section-gray .left-right .inner-right .icon {
    flex-basis: calc(50% - 15px);
  }
  .section-gray .left-right .inner-right .inner-text:nth-child(1),
  .section-gray .left-right .inner-right .icon:nth-child(1) {
    flex-basis: calc(50% - 15px);
  }
  .section-gray .left-right .inner-right .icon {
    width: 150px;
  }
  .section-inspired .inspired-wrapper .inspired-image img {
    min-height: 100%;
    object-position: center;
  }
  .section-inspired .inspired-wrapper .inspired-image .section-title {
    font-size: 1.5rem;
    text-align: center;
  }
  .section-inspired .inspired-wrapper .inspired-image .wrapper {
    top: unset;
    bottom: 15px;
    width: 250px;
  }
  .section-inspired .inspired-wrapper .inspired-image .wrapper .top {
    font-size: 1.5rem;
  }
  .section-inspired .inspired-wrapper .inspired-image .wrapper .bottom {
    width: 100%;
    font-size: 0.875rem;
  }
  .section-inspired .video-container,
  .section-movie .video-container {
    min-height: 175px !important;
  }
  .section-inspired .video-container .video-placeholder img,
  .section-movie .video-container .video-placeholder img {
    min-height: fit-content;
    object-position: center;
  }
  .section-bestseller {
    padding-bottom: 3rem;
  }
  .section-bestseller .wrapper {
    position: relative;
  }
  .section-bestseller .wrapper .swiper-button-container {
    position: absolute;
    display: flex;
    justify-content: center;
    width: 100%;
  }
  .section-bestseller .wrapper .swiper-button-container .swiper-button-next {
    right: 35%;
    left: auto;
  }
  .section-bestseller .wrapper .swiper-button-container .swiper-button-prev {
    right: auto;
    left: 35%;
  }
  .section-bestseller .product .product-item {
    padding-top: 1rem;
  }
  .section-bestseller .product .product-item img {
    height: 300px !important;
  }
  .section-for .category-section {
    flex-direction: column;
    gap: 35px;
    overflow: hidden;
  }
  .section-for .category-section .category-image ul {
    top: 30%;
  }
  .section-for .category-section .category-image img {
    max-height: 135px;
    object-position: 250% 45%;
  }
  .section-for .category-section .category-content {
    flex-basis: 100%;
    min-width: 100%;
    position: relative;
  }
  .section-for .category-section .category-content .swiper-button-container {
    position: absolute;
    display: flex;
    justify-content: center;
    width: 100%;
    top: 25px;
  }
  .section-for .category-section .category-content .swiper-button-container .swiper-button-next {
    right: 0;
    left: auto;
  }
  .section-for .category-section .category-content .swiper-button-container .swiper-button-prev {
    display: none;
  }
  .section-for .category-section .category-content .swiperFor .swiper-wrapper .swiper-slide {
    flex-shrink: 0;
  }
  .section-for .category-section .category-content .swiperFor .swiper-wrapper .swiper-slide .product .product-item {
    padding-top: 1rem;
  }
  .section-quality .wrapper {
    gap: 30px;
  }
  .section-quality .wrapper .insider {
    gap: 30px;
    justify-content: space-between;
  }
  .section-quality .wrapper .insider .column {
    width: 100%;
  }
  .section-quality .wrapper .insider .column .section-title {
    margin-bottom: 0;
  }
  .section-quality .wrapper .insider .column .content .icon {
    max-width: 120px;
  }
  .section-quality .wrapper .insider .column .content .text {
    text-align: left;
    font-size: 1.125rem;
  }
  .section-cards .wrapper .card {
    min-height: 215px;
  }
  .section-cards .wrapper .card:nth-child(1) {
    min-height: 220px;
  }
  .section-cards .wrapper .card .card-header {
    flex-wrap: wrap;
    padding: 4rem 0;
  }
  .section-cards .wrapper .card .card-header .icon .section-title {
    font-size: 2.25rem;
  }
  .section-cards .wrapper .card .card-content {
    flex-direction: column;
    gap: 25px;
  }
  .section-cards .wrapper .card .card-content .image {
    flex-basis: 100%;
    width: 100%;
  }
  .section-cards .wrapper .card .card-content .image img {
    object-fit: contain;
    aspect-ratio: 1/1;
  }
  .section-cards .wrapper .card .card-content .description {
    flex-basis: 100%;
    font-size: 1rem;
  }
  .section-cards .wrapper .card .card-content .products {
    grid-template-columns: repeat(2, 1fr);
    justify-content: center;
  }
  .section-movie .wrapper .section-header .section-title {
    font-size: 3rem;
  }
  .section-movie .wrapper .section-header .section-subtitle {
    font-size: 1rem;
    max-width: 100%;
  }
  .section-movie .wrapper .section-content {
    flex-direction: column;
  }
  .section-movie .wrapper .section-content .movie {
    flex: 100%;
    width: 100%;
  }
  .section-movie .wrapper .section-content .movie-info {
    min-height: fit-content;
    width: 100%;
  }
  .section-movie .wrapper .section-content .movie-info .insider {
    padding: 1rem;
    width: 100%;
  }
  .section-find-us {
    flex-direction: column;
  }
  .section-find-us .wrapper {
    padding-top: 2rem;
    justify-content: flex-start;
  }
  .section-find-us .wrapper .insider {
    flex-basis: 100%;
    padding: 0;
  }
  .section-more-inspiration {
    padding-bottom: 0;
  }
  .section-more-inspiration .wrapper {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    position: relative;
  }
  .section-more-inspiration .wrapper .section-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
  .section-more-inspiration .wrapper .swiper-button-container {
    position: relative;
    bottom: 10px;
    right: 50%;
    padding: 2rem 0;
  }
  .section-more-inspiration .wrapper .swiper-button-container .swiper-button-next {
    left: auto;
    right: -50px;
  }
  .section-more-inspiration .wrapper .swiper-button-container .swiper-button-prev {
    right: var(--swiper-navigation-sides-offset, 20px);
    left: auto;
  }
  .section-more-inspiration .swiperMoreInspiration .swiper-slide .post-thumbnail {
    max-height: 200px;
  }
}
@media (max-width: 767.98px) {
  .section-category .grid {
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }
  .section-category .grid .category-item {
    max-height: 60px;
    min-height: unset;
  }
  .section-category .grid .category-item svg,
  .section-category .grid .category-item hr {
    display: none;
  }
  .section-category .grid .category-item:last-of-type {
    max-height: 60px;
    min-height: unset;
  }
  .section-category .grid .category-item .category-link {
    overflow: clip;
  }
  .section-category .grid .category-item .category-link .category-content {
    height: 100%;
  }
  .section-category .grid .category-item .category-link .category-content .insider {
    height: 100%;
  }
  .opinie-section {
    padding: 40px 0;
  }
  .opinie-section .section-title {
    font-size: 1.5rem;
  }
  .opinie-section .ti-widget.ti-goog .ti-reviews-container .ti-controls .ti-next {
    right: 35% !important;
  }
  .opinie-section .ti-widget.ti-goog .ti-reviews-container .ti-controls .ti-prev {
    left: 35% !important;
  }
  .section-gray .left-right .inner-left .section-title {
    font-size: 2rem;
  }
  .section-gray .left-right .inner-left .dd-black-border-button {
    text-align: center;
  }
  .section-gray .left-right .inner-right .inner-text,
  .section-gray .left-right .inner-right .icon {
    flex-basis: calc(50% - 15px);
  }
  .section-for .category-section .category-content .category-text {
    font-size: 1.75rem;
    line-height: unset;
    flex-wrap: wrap;
  }
  .section-for .category-section .category-content .category-text .main-button {
    padding: 15px;
  }
  .section-for .category-section .category-content .product-list .woocommerce ul.products {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 767.98px) and (max-width: 380px) {
  .section-for .category-section .category-content .swiper-button-container {
    position: relative;
    top: unset;
  }
  .section-for .category-section .category-content .swiper-wrapper {
    padding-top: 2rem;
  }
}
@media (max-width: 767.98px) {
  .section-quality .wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 0;
  }
  .section-quality .wrapper .insider .column,
  .section-quality .wrapper .insider .column:not(:first-of-type) {
    flex-basis: 100%;
    width: 100%;
  }
  .section-quality .wrapper .insider .column .content,
  .section-quality .wrapper .insider .column:not(:first-of-type) .content {
    flex-wrap: wrap;
    justify-content: center;
  }
  .section-quality .wrapper .insider .column .section-title,
  .section-quality .wrapper .insider .column:not(:first-of-type) .section-title {
    font-size: 1.75rem;
    line-height: 110%;
  }
  .section-inspired-posts .wrapper {
    display: flex;
    justify-content: space-between;
  }
  .section-inspired-posts .wrapper .section-title {
    font-size: 1.5rem;
  }
  .section-inspired-posts .wrapper .swiper-button-container {
    top: 25px;
  }
}
@media screen and (max-width: 767.98px) and (max-width: 550px) {
  .section-inspired-posts .wrapper .swiper-button-container {
    position: relative;
    padding: 4.5rem 0;
    right: 50px;
  }
}
@media (max-width: 767.98px) {
  .section-cards .wrapper .card:nth-child(1) {
    min-height: 210px;
  }
  .section-cards .wrapper .card .card-header {
    flex-wrap: wrap;
    padding: 2rem 0;
  }
  .section-cards .wrapper .card .card-header .icon {
    width: 100%;
  }
  .section-cards .wrapper .card .card-header .icon .section-title {
    font-size: 1.5rem;
  }
  .section-cards .wrapper .card .card-content .products {
    display: grid;
    justify-content: unset;
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 767.98px) and (max-width: 550px) {
  .section-cards .wrapper .card {
    min-height: 270px;
  }
  .section-cards .wrapper .card:nth-child(1) {
    min-height: 270px;
  }
  .section-cards .wrapper .card .card-header .main-button {
    width: 100%;
  }
}
@media (max-width: 767.98px) {
  .section-find-us .wrapper .insider .section-title {
    font-size: 2rem;
    line-height: 1.2;
  }
}
@media (max-width: 320px) {
  .section-gray .left-right .inner-right .inner-text,
  .section-gray .left-right .inner-right .icon {
    flex-basis: 100%;
  }
  .section-inspired-posts .wrapper .section-title {
    font-size: 1.25rem;
  }
  .section-inspired-posts .wrapper .swiper-button-container {
    top: 30px;
  }
  .section-bestseller .wrapper .swiper-button-container .swiper-button-next {
    right: 30%;
    left: auto;
  }
  .section-bestseller .wrapper .swiper-button-container .swiper-button-prev {
    right: auto;
    left: 30%;
  }
  .opinie-section {
    padding-top: 0;
  }
  .opinie-section .section-title {
    font-size: 1.5rem;
  }
  .opinie-section .ti-widget.ti-goog .ti-reviews-container .ti-controls .ti-next {
    right: 25% !important;
  }
  .opinie-section .ti-widget.ti-goog .ti-reviews-container .ti-controls .ti-prev {
    left: 25% !important;
  }
  .section-inspired .inspired-wrapper .swiperInspired .swiper-button-container .swiper-button-next {
    right: 30%;
  }
  .section-inspired .inspired-wrapper .swiperInspired .swiper-button-container .swiper-button-prev {
    left: 30%;
  }
}
/* Mobile subcategories list */
.dd-mobile-subcategories {
  margin: 20px 0;
  padding: 0;
}
.dd-mobile-subcategories .dd-mobile-subcategories-title {
  font-family: "Lato", sans-serif;
  font-size: 1rem;
  font-weight: 500;
  color: #000000;
  margin: 0 0 15px 0;
  letter-spacing: 1px;
  text-transform: uppercase;
}
.dd-mobile-subcategories .dd-mobile-subcategories-wrapper {
  position: relative;
}
.dd-mobile-subcategories .dd-mobile-subcategories-list {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  gap: 10px;
  padding: 10px 0 15px 0;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  position: relative;
}
.dd-mobile-subcategories .dd-mobile-subcategories-list::-webkit-scrollbar {
  display: none;
}
.dd-mobile-subcategories .dd-mobile-subcategories-list::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8));
  pointer-events: none;
}
.dd-mobile-subcategories .dd-mobile-subcategories-list .dd-subcategory-item {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  flex-shrink: 0;
  padding: 10px 25px;
  color: #000000;
  text-decoration: none;
  width: fit-content;
  transition: 0.2s ease-out;
  border: 1px solid #ddd;
}
.dd-mobile-subcategories .dd-mobile-subcategories-list .dd-subcategory-item:hover, .dd-mobile-subcategories .dd-mobile-subcategories-list .dd-subcategory-item:focus {
  border: 1px solid #ddd;
  color: #FFFFFF;
  transition: 0.2s ease-out;
  background-color: rgba(255, 255, 255, 0.1294117647);
}
.dd-mobile-subcategories .dd-mobile-subcategories-list .dd-subcategory-item:focus {
  outline: 2px solid #4A6A65;
  outline-offset: 2px;
}
.dd-mobile-subcategories .dd-mobile-subcategories-list .dd-subcategory-item:active {
  transform: translateY(0);
}
.dd-mobile-subcategories .dd-mobile-subcategories-list .dd-subcategory-item .dd-subcategory-name {
  font-family: "Lato", sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  color: #000000;
  text-align: center;
  line-height: 1.2;
}

@media only screen and (max-width: 991.98px) {
  .desktop {
    display: none;
  }
  .mobile {
    display: block;
  }
  .mobile-nav-menu li {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    transition: 0.3s ease-in-out;
  }
  .mobile-nav-menu li .fa-solid.fa-chevron-down {
    display: none;
  }
  .mobile-nav-menu li.menu-item-has-children > a {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .mobile-nav-menu li.menu-item-has-children > a .fa-solid.fa-chevron-down {
    display: block;
    font-size: 14px;
    font-weight: 900;
    transition: transform 0.3s ease;
  }
  .mobile-nav-menu li.menu-item-has-children.menu-open > .sub-menu {
    display: block;
  }
  .mobile-nav-menu li.menu-item-has-children.menu-open > a .fa-solid.fa-chevron-down {
    transform: rotate(180deg);
  }
  .mobile-nav-menu .sub-menu {
    display: none;
  }
  body:not(.single-product) .product .product-item {
    gap: 20px;
  }
  body:not(.single-product) .product .product-item .product-variations {
    min-height: unset;
  }
  body:not(.single-product) .product .product-item .product-variations .variations .variation-button {
    padding: 8px;
    width: calc(50% - 8px);
  }
  body:not(.single-product) .product .product-item .product-variations .variations,
  body:not(.single-product) .product .product-item .product-variations .single_add_to_cart_button {
    visibility: visible;
  }
  body:not(.single-product) .product .product-item .product-variations .single_add_to_cart_button {
    padding: 10px 5px;
  }
  ul.products {
    grid-template-columns: repeat(2, 1fr);
  }
  ul.products .product .product-item .product-link .price {
    margin-top: unset;
  }
  .mini-cart .cart-item-details .cart-item-column,
  #dd-checkout-items-summary .cart-item-details .cart-item-column {
    flex-wrap: wrap;
  }
  .checkout-accordion-item .checkout-accordion-content .coupon-form {
    display: flex;
    flex-direction: column;
  }
  .checkout-accordion-item .checkout-accordion-content .coupon-form .form-row.form-row-first, .checkout-accordion-item .checkout-accordion-content .coupon-form .form-row.form-row-last {
    width: 100%;
  }
  .single-product-wrapper {
    flex-direction: column;
    padding: 10px;
  }
  .dd-mobile-subcategories {
    margin: 15px 0;
  }
  .dd-mobile-subcategories .dd-mobile-subcategories-title {
    font-size: 0.875rem;
    margin-bottom: 10px;
  }
  .dd-mobile-subcategories .dd-mobile-subcategories-list {
    gap: 8px;
  }
  .dd-mobile-subcategories .dd-mobile-subcategories-list .dd-subcategory-item .dd-subcategory-name {
    font-size: 13px;
  }
}
@media only screen and (max-width: 400px) {
  .quantity-and-cart {
    flex-wrap: wrap;
  }
}
@media only screen and (max-width: 767.98px) {
  .dd-container {
    padding: 0 15px;
  }
  ul.products {
    grid-template-columns: repeat(1, 1fr);
  }
  .product-variations .variations {
    flex-wrap: wrap;
  }
  body:not(.single-product) .product .product-item .product-variations .variations .variation-button.three-variations {
    width: calc(50% - 8px);
  }
  body:not(.single-product) .product .product-item .product-variations .variations .variation-button.three-variations:last-of-type {
    width: 100%;
  }
}
@media only screen and (max-width: 320px) {
  .input-group input[type=email] {
    max-width: 180px;
  }
  .dd-mobile-subcategories .dd-mobile-subcategories-list .dd-subcategory-item {
    padding: 8px 10px;
  }
  .dd-mobile-subcategories .dd-mobile-subcategories-list .dd-subcategory-item .dd-subcategory-name {
    font-size: 12px;
  }
}
/* Breadcrumbs */
.dd-breadcrumbs a {
  font-weight: 300;
  text-decoration: none;
  color: #737373;
}
.dd-breadcrumbs .breadcrumb_last {
  font-weight: 500;
  text-decoration: none;
  color: #6e6e6e !important;
}

/*# sourceMappingURL=style.css.map */
