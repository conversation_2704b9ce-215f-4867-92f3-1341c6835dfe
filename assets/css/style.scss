@charset "UTF-8";
@import "_variables";
@import "_fonts.scss";
@import "_woocommerce";

* {
    box-sizing: border-box;
}

.dd-container {
    margin: auto;
    max-width: 1440px;
    width: 100%;
    padding: 0 50px;
}

.desktop {
    display: block;
}

.mobile {
    display: none;
}

.page-content {
    padding-bottom: 30px;
}

.sr-only {
    border: 0 !important;
    clip: rect(1px, 1px, 1px, 1px) !important;
    -webkit-clip-path: inset(50%) !important;
    clip-path: inset(50%) !important;
    height: 1px !important;
    overflow: hidden !important;
    margin: -1px !important;
    padding: 0 !important;
    position: absolute !important;
    width: 1px !important;
    white-space: nowrap !important;
}

.black_text {
    color: #000000 !important;
}

.main-button {
    &.black_text {
        border-color: #000000 !important;
    }
}

.products {
    display: grid;
    grid-template-rows: 1fr;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 15px;

    &::before {
        display: none !important;
    }

    .product {
        display: flex;
        flex-direction: column;

        form.cart {
            margin: 0 !important;
        }

        .product-item {
            height: 100%;
            justify-content: space-between;

            .product-link {
                display: flex;
                flex-direction: column;
                height: 100%;
                width: 100%;

                .price {
                    margin-top: auto;
                    margin-bottom: 1rem;
                }
            }
        }
    }
}

@mixin button-hover-focus-active {
    &:hover,
    &:focus,
    &:active,
    &.checked {
        border-color: $color-primary;
        outline: 1px solid $color-primary;
    }
}

.product-variations {
    span {
        font-weight: 300;
        color: var(--text-gray);
    }

    .variations {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;

        .variation-button {
            background: none;
            color: inherit;
            border: 1px solid $border-color;
            padding: $button-padding;
            font: inherit;
            cursor: pointer;
            outline: inherit;
            font-family: $font-primary;
            white-space: pre;

            &.out-of-stock {
                background-color: $background-out-of-stock;
                color: $text-out-of-stock;
            }

            @include button-hover-focus-active;
        }
    }
}

.single-product {
    .product-details {
        .price {
            margin-top: 50px;
        }

        .single_add_to_cart_button {
            &.disabled {
                display: none;
            }
        }
        .product-variations {
            margin-top: 20px;
        }
    }
}

body:not(.single-product) {
    .product {
        .product-item {
            position: relative;

            .variations,
            .single_add_to_cart_button {
                visibility: hidden;
            }

            &.active {
                .variations,
                .single_add_to_cart_button {
                    visibility: visible;
                }
            }

            .product-variations {
                display: flex;
                flex-direction: column;
                position: relative;
                gap: 15px;
                width: 100%;
                min-height: 150px;
                font-family: $font-primary;

                .variations {
                    .variation-button {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border: 1px solid $border-color;
                        padding: 8px 38px;

                        &.out-of-stock {
                            cursor: not-allowed;
                        }

                        &.variation-1 {
                            width: calc(100% - 10px);
                        }

                        &.variation-2 {
                            width: calc(50% - 8px);
                        }

                        &.variation-3 {
                            padding: 8px 16px;
                            width: calc(33.33% - 10px);
                        }

                        &.variation-4 {
                            padding: 8px 16px;
                            width: calc(50% - 8px);
                        }

                        &.variation-5 {
                            padding: 8px 16px;
                            width: calc(33.33% - 10px);
                        }

                        &.variation-6 {
                            padding: 8px 16px;
                            width: calc(33.33% - 10px);
                        }

                        @include button-hover-focus-active;
                    }
                }
            }

            form.cart {
                .quantity {
                    display: none;
                }

                .single_add_to_cart_button {
                    margin: 0;
                    padding: 10px 25px;
                    width: 100%;

                    &.disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                }
            }
        }
    }
}

.woocommerce ul.products li.product .price {
    del,
    ins {
        height: 20px;
    }
}

[disabled] {
    cursor: not-allowed;
}

.swiper-button-next,
.swiper-button-prev {
    background-size: 35%;
    background-position: center;
    background-repeat: no-repeat;
    border: 1px solid #4A6A65;
    border-radius: 50%;
    filter: $filter;
    width: 45px !important;
    height: 45px !important;
    transition: $transition;

    &::after,
    &::before {
        display: none !important;
    }

    &:hover {
        background-color: $swiper-button-hover !important;
        border: 1px solid transparent;
        transition: $transition;
        filter: unset;
    }
}

.swiper-button-prev {
    background-image: url(../../public/arrow-left.svg) !important;
}

.swiper-button-next {
    background-image: url(../../public/arrow-right.svg) !important;
}

.swiper-wrapper {
    ul {
        padding-inline-start: 0px;
    }
}

.dd-product-tabs {
    margin-top: 20px;
    padding-top: 20px;

    .tabs {
        display: flex;
        border-bottom: 1px solid #d3d3d3;

        .tab {
            padding: 20px 65px;
            text-align: center;
            cursor: pointer;
            letter-spacing: 0.235px;
            color: #474747;
            font-weight: 400;
            text-transform: uppercase;
            outline: 2px solid transparent;

            &.active {
                font-weight: 600;
                border-bottom: 2px solid #787878;
                outline: 2px solid transparent;
            }
        }
    }

    .dd-product-full-description {
        .tab-panel {
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;

            &.active {
                display: block;
            }

            &.fade-in {
                opacity: 1;
                transition: opacity 0.3s ease;
            }
        }
    }
}

.dd-product-full-description {
    img.alignright {
        float: right;
        margin-left: 20px;
        margin-bottom: 10px;
    }

    img.alignleft {
        float: left;
        margin-right: 20px;
        margin-bottom: 10px;
    }
}

.dd-products-opinions {
    padding-top: 50px;
    padding-bottom: 50px;
}

.categorie-section {
    .woocommerce-products-header {
        .term-description {
            font-size: $font-size-16px;

            p:not(.details p) {
                color: #615f59;
            }

            details {
                summary {
                    font-size: $font-size-16px !important;
                    color: #615f59;
                }
            }
        }
    }

    .term-description {
        line-height: 1.5;
        font-weight: 300;
        padding: 20px 0 40px;

        details {
            summary {
                font-size: $font-size-20px !important;
                color: #615f59 !important;
            }

            p {
                font-size: $font-size-16px;
            }
        }
    }
}

.product-link {
    &:hover {
        transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
        .dd-product-image {
            &:not(.has-second-image) {
                opacity: 1;
            }

            .dd-product-second-image {
                opacity: 1;
            }
        }
    }

    .dd-product-image {
        width: 100%;

        img {
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
        }

        .dd-product-second-image {
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
        }
    }
}

@import "frontpage";

/* Mobile subcategories list */
.dd-mobile-subcategories {
    margin: 20px 0;
    padding: 0;

    .dd-mobile-subcategories-title {
        font-family: $font-primary;
        font-size: $font-size-16px;
        font-weight: 500;
        color: $color-black;
        margin: 0 0 15px 0;
        letter-spacing: 1px;
        text-transform: uppercase;
    }

    .dd-mobile-subcategories-wrapper {
        position: relative;
    }

    .dd-mobile-subcategories-list {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: auto;
        gap: 10px;
        padding: 10px 0 15px 0;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
        position: relative;

        &::-webkit-scrollbar {
            display: none;
        }

        // Gradient fade effect on the right
        &::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 30px;
            height: 100%;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8));
            pointer-events: none;
        }

        .dd-subcategory-item {
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            flex-shrink: 0;
            padding: 10px 25px;
            color: $color-black;
            text-decoration: none;
            width: fit-content;
            transition: $transition;
            border: 1px solid #ddd;

            &:hover,
            &:focus {
                border: 1px solid #ddd;
                color: $color-white;
                transition: $transition;
                background-color: $button-hover;
            }

            &:focus {
                outline: 2px solid $color-primary;
                outline-offset: 2px;
            }

            &:active {
                transform: translateY(0);
            }

            .dd-subcategory-name {
                font-family: $font-primary;
                font-size: $font-size-14px;
                font-weight: 400;
                color: $color-black;
                text-align: center;
                line-height: 1.2;
            }
        }
    }
}

@media only screen and (max-width: $media-tablet) {
    .desktop {
        display: none;
    }
    .mobile {
        display: block;
    }

    .mobile-nav-menu {
        li {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100%;
            transition: 0.3s ease-in-out;

            .fa-solid.fa-chevron-down {
                display: none;
            }

            &.menu-item-has-children {
                > a {
                    display: flex;
                    align-items: center;
                    gap: 10px;

                    .fa-solid.fa-chevron-down {
                        display: block;
                        font-size: 14px;
                        font-weight: 900;
                        transition: transform 0.3s ease;
                    }
                }

                &.menu-open {
                    > .sub-menu {
                        display: block;
                    }

                    > a .fa-solid.fa-chevron-down {
                        transform: rotate(180deg);
                    }
                }
            }
        }

        .sub-menu {
            display: none;
        }
    }

    body:not(.single-product) {
        .product {
            .product-item {
                gap: 20px;
                .product-variations {
                    min-height: unset;
                    .variations {
                        .variation-button {
                            padding: 8px;
                            width: calc(50% - 8px);
                        }
                    }
                    .variations,
                    .single_add_to_cart_button {
                        visibility: visible;
                    }

                    .single_add_to_cart_button {
                        padding: 10px 5px;
                    }
                }
            }
        }
    }

    ul.products {
        grid-template-columns: repeat(2, 1fr);
        .product {
            .product-item {
                .product-link {
                    .price {
                        margin-top: unset;
                    }
                }
            }
        }
    }

    .mini-cart,
    #dd-checkout-items-summary {
        .cart-item-details {
            .cart-item-column {
                flex-wrap: wrap;
            }
        }
    }

    .checkout-accordion-item {
        .checkout-accordion-content {
            .coupon-form {
                display: flex;
                flex-direction: column;

                .form-row {
                    &.form-row-first,
                    &.form-row-last {
                        width: 100%;
                    }
                }
            }
        }
    }

    .single-product-wrapper {
        flex-direction: column;
        padding: 10px;
    }

    // Mobile subcategories responsive adjustments
    .dd-mobile-subcategories {
        margin: 15px 0;

        .dd-mobile-subcategories-title {
            font-size: $font-size-14px;
            margin-bottom: 10px;
        }

        .dd-mobile-subcategories-list {
            gap: 8px;

            .dd-subcategory-item {
                .dd-subcategory-name {
                    font-size: 13px;
                }
            }
        }
    }
}

@media only screen and (max-width: 400px) {
    .quantity-and-cart {
        flex-wrap: wrap;
    }
}

@media only screen and (max-width: $media-mobile) {
    .dd-container {
        padding: 0 15px;
    }

    ul.products {
        grid-template-columns: repeat(1, 1fr);
    }

    .product-variations {
        .variations {
            flex-wrap: wrap;
        }
    }

    body:not(.single-product) {
        .product {
            .product-item {
                .product-variations {
                    .variations {
                        .variation-button {
                            &.three-variations {
                                width: calc(50% - 8px);
                                &:last-of-type {
                                    width: 100%;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: $media-mobile-sm) {
    .input-group input[type="email"] {
        max-width: 180px;
    }

    // Extra small screens adjustments for subcategories
    .dd-mobile-subcategories {
        .dd-mobile-subcategories-list {
            .dd-subcategory-item {
                padding: 8px 10px;

                .dd-subcategory-name {
                    font-size: 12px;
                }
            }
        }
    }
}

/* Breadcrumbs */

.dd-breadcrumbs
{
    a {
        font-weight: 300;
        text-decoration: none;
        color: #737373;
    }

    .breadcrumb_last {
        font-weight: 500;
        text-decoration: none;
        color: #6e6e6e !important;
    }
}
