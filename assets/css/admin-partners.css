/* Admin Partners Styles */
.dd-partners-admin {
    max-width: 1400px;
}

.dd-partners-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.dd-partners-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.dd-partners-header .button {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Empty State */
.dd-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.dd-empty-row .dd-empty-state {
    padding: 40px 20px;
}

.dd-empty-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
}

.dd-empty-state h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.dd-empty-state p {
    margin: 0;
    font-size: 14px;
}

/* Table Styles */
.dd-partners-table {
    margin-top: 0;
}

.dd-partners-table th,
.dd-partners-table td {
    vertical-align: middle;
}

.dd-partners-table .column-order {
    width: 80px;
    text-align: center;
}

.dd-partners-table .column-preview {
    width: 120px;
}

.dd-partners-table .column-details {
    width: auto;
}

.dd-partners-table .column-status {
    width: 100px;
    text-align: center;
}

.dd-partners-table .column-actions {
    width: 120px;
    text-align: center;
}

/* Partner Row */
.dd-partner-row {
    position: relative;
}

.dd-partner-row.error {
    background-color: #ffeaea;
}

.dd-partner-row.ui-sortable-helper {
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Drag Handle */
.dd-drag-handle {
    cursor: move;
    color: #666;
    margin-right: 8px;
}

.dd-drag-handle:hover {
    color: #0073aa;
}

.partner-number {
    font-weight: 600;
    color: #666;
}

/* Preview */
.dd-partner-preview {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
}

.preview-logo {
    max-width: 100px;
    max-height: 60px;
    width: auto;
    height: auto;
    object-fit: contain;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
    background: #fff;
}

.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 60px;
    border: 2px dashed #ddd;
    border-radius: 4px;
    color: #999;
    font-size: 12px;
    text-align: center;
}

.preview-placeholder .dashicons {
    font-size: 24px;
    margin-bottom: 4px;
}

/* Details */
.partner-details {
    padding: 5px 0;
}

.partner-alt-text {
    margin-bottom: 5px;
    font-size: 13px;
}

.partner-alt-display {
    color: #666;
    font-style: italic;
}

/* Status */
.partner-status {
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-active {
    color: #46b450;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

.status-inactive {
    color: #dc3232;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

/* Actions */
.partner-actions {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.partner-actions .button {
    padding: 4px 8px;
    min-height: auto;
    line-height: 1;
}

.partner-actions .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Modal Styles */
.dd-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dd-modal-content {
    background: #fff;
    border-radius: 4px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.dd-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 15px;
    border-bottom: 1px solid #ddd;
}

.dd-modal-header h2 {
    margin: 0;
    font-size: 18px;
}

.dd-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dd-modal-close:hover {
    color: #000;
}

.dd-modal-body {
    padding: 20px;
}

.dd-modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #ddd;
    margin-top: 20px;
}

/* Form Styles */
.dd-partner-edit-form .form-table th {
    width: 150px;
    padding-left: 0;
}

.dd-partner-edit-form .form-table td {
    padding-right: 0;
}

.required {
    color: #dc3232;
}

.dd-image-upload {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.image-preview {
    min-height: 100px;
    border: 2px dashed #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fafafa;
}

.image-preview img {
    max-width: 200px;
    max-height: 100px;
    object-fit: contain;
}

.image-buttons {
    display: flex;
    gap: 10px;
}

/* Messages */
.dd-message {
    padding: 12px 16px;
    margin-bottom: 20px;
    border-radius: 4px;
    font-weight: 600;
}

.dd-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.dd-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Sortable Placeholder */
.ui-sortable-placeholder {
    background: #f0f0f1;
    border: 2px dashed #c3c4c7;
    height: 60px;
}

.ui-sortable-placeholder td {
    border: none !important;
    background: transparent !important;
    text-align: center;
    vertical-align: middle;
    color: #666;
    font-style: italic;
}

/* Error States */
input.error {
    border-color: #dc3232 !important;
    box-shadow: 0 0 0 1px #dc3232 !important;
}

/* Responsive */
@media (max-width: 782px) {
    .dd-partners-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .dd-partners-actions {
        display: flex;
        gap: 10px;
    }
    
    .dd-partners-table .column-preview {
        width: 80px;
    }
    
    .preview-logo {
        max-width: 60px;
        max-height: 40px;
    }
    
    .preview-placeholder {
        width: 60px;
        height: 40px;
        font-size: 10px;
    }
    
    .partner-actions {
        flex-direction: column;
        gap: 2px;
    }
    
    .dd-modal-content {
        width: 95%;
        margin: 20px;
    }
}
