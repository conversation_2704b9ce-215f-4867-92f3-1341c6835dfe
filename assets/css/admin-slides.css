/* Admin Slides Styles */
.dd-slides-admin {
    max-width: 1400px;
}

.dd-slides-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.dd-slides-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.dd-slides-header .button {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Empty State */
.dd-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.dd-empty-row .dd-empty-state {
    padding: 40px 20px;
}

.dd-empty-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
}

.dd-empty-state h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.dd-empty-row {
    background: #f9f9f9;
}

.dd-empty-row:hover {
    background: #f9f9f9;
}

/* Table Styles */
.dd-slides-table-container {
    margin-bottom: 20px;
}

.dd-slides-table {
    border-collapse: collapse;
}

.dd-slides-table th {
    background: #f9f9f9;
    font-weight: 600;
    padding: 12px 8px;
    border-bottom: 2px solid #ddd;
}

.dd-slides-table .column-order {
    width: 80px;
    text-align: center;
}

.dd-slides-table .column-preview {
    width: 200px;
}

.dd-slides-table .column-details {
    width: auto;
}

.dd-slides-table .column-status {
    width: 120px;
    text-align: center;
}

.dd-slides-table .column-actions {
    width: 120px;
    text-align: center;
}

/* Table Row Styles */
.dd-slide-row {
    transition: all 0.3s ease;
}

.dd-slide-row:hover {
    background-color: #f8f9fa;
}

.dd-slide-row.ui-sortable-helper {
    background: #fff;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    border: 1px solid #0073aa;
}

.dd-slide-row td {
    padding: 15px 8px;
    vertical-align: middle;
    border-bottom: 1px solid #ddd;
}

/* Drag Handle */
.dd-drag-handle {
    color: #666;
    cursor: grab;
    margin-right: 8px;
    font-size: 16px;
}

.dd-drag-handle:hover {
    color: #0073aa;
}

.dd-drag-handle:active {
    cursor: grabbing;
}

.slide-number {
    font-weight: 600;
    color: #333;
}

/* Preview Styles */
.dd-slide-preview {
    position: relative;
    display: flex;
    gap: 10px;
    align-items: center;
}

.dd-slide-preview img {
    max-width: 80px;
    max-height: 60px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    object-fit: cover;
}

.preview-desktop {
    border: 2px solid #0073aa;
}

.preview-mobile {
    border: 2px solid #00a32a;
    max-width: 40px;
    max-height: 60px;
}

.mobile-indicator {
    position: absolute;
    bottom: -5px;
    right: 0;
    background: #00a32a;
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: 600;
}

.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 60px;
    background: #f0f0f0;
    border: 2px dashed #ccc;
    border-radius: 4px;
    color: #666;
    font-size: 11px;
}

.preview-placeholder .dashicons {
    font-size: 20px;
    margin-bottom: 2px;
}

/* Slide Details */
.slide-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.slide-alt {
    color: #333;
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
}

.slide-link-display {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-size: 12px;
}

.slide-link-display .dashicons {
    font-size: 14px;
}

.slide-link-display code {
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

/* Status Styles */
.slide-status {
    display: flex;
    justify-content: center;
}

.status-active,
.status-inactive {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

/* Action Buttons */
.slide-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.slide-actions .button {
    padding: 4px 8px;
    min-height: auto;
    line-height: 1;
}

.slide-actions .button .dashicons {
    font-size: 16px;
    line-height: 1;
}

.dd-edit-slide {
    color: #0073aa;
    border-color: #0073aa;
}

.dd-edit-slide:hover {
    background: #0073aa;
    color: #fff;
}

.dd-toggle-slide {
    color: #666;
    border-color: #666;
}

.dd-toggle-slide:hover {
    background: #666;
    color: #fff;
}

.dd-delete-slide {
    color: #d63638;
    border-color: #d63638;
}

.dd-delete-slide:hover {
    background: #d63638;
    color: #fff;
}

/* Modal Styles */
.dd-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dd-modal-content {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.dd-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #ddd;
    background: #f9f9f9;
    border-radius: 8px 8px 0 0;
}

.dd-modal-header h2 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.dd-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.dd-modal-close:hover {
    background: #f0f0f0;
    color: #333;
}

.dd-modal-body {
    padding: 25px;
}

.dd-modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #ddd;
    margin-top: 20px;
}

/* Form Styles */
.dd-slide-edit-form .form-table {
    margin: 0;
}

.dd-slide-edit-form .form-table th {
    width: 150px;
    padding: 15px 10px 15px 0;
    vertical-align: top;
}

.dd-slide-edit-form .form-table td {
    padding: 15px 0;
}

.dd-image-upload {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 100%;
}

.dd-image-upload .image-preview {
    border: 2px dashed #ddd;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    background: #fafafa;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dd-image-upload .image-preview img {
    max-width: 100%;
    max-height: 200px;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.dd-image-upload .image-preview:empty::before {
    content: "Brak wybranego zdjęcia";
    color: #666;
    font-style: italic;
}

.image-buttons {
    display: flex;
    gap: 10px;
}

.image-buttons .button {
    flex: 0 0 auto;
}

.dd-slides-actions {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.dd-delete-slide {
    color: #a00;
    border-color: #a00;
}

.dd-delete-slide:hover {
    background: #a00;
    color: #fff;
}

.required {
    color: #d63638;
}

/* Loading states */
.dd-slide-item.loading {
    opacity: 0.6;
    pointer-events: none;
}

.dd-slide-item.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #0073aa;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Success/Error messages */
.dd-message {
    padding: 10px 15px;
    margin: 15px 0;
    border-radius: 4px;
    font-weight: 500;
}

.dd-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.dd-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Responsive */
@media (max-width: 1200px) {
    .dd-slides-table .column-details {
        min-width: 200px;
    }

    .slide-details {
        font-size: 12px;
    }

    .dd-slide-preview img {
        max-width: 60px;
        max-height: 45px;
    }
}

@media (max-width: 768px) {
    .dd-slides-header {
        flex-direction: column;
        gap: 10px;
    }

    .dd-slides-header .button {
        width: 100%;
        justify-content: center;
    }

    .dd-slides-table {
        font-size: 12px;
    }

    .dd-slides-table .column-preview {
        width: 120px;
    }

    .dd-slide-preview {
        flex-direction: column;
        gap: 5px;
    }

    .dd-slide-preview img {
        max-width: 50px;
        max-height: 35px;
    }

    .slide-actions {
        flex-direction: column;
        gap: 2px;
    }

    .slide-actions .button {
        padding: 2px 4px;
        min-width: 30px;
    }

    .dd-modal-content {
        width: 95%;
        margin: 20px;
    }

    .dd-modal-header {
        padding: 15px 20px;
    }

    .dd-modal-body {
        padding: 20px;
    }
}

/* Sortable placeholder */
.dd-slides-table .ui-sortable-placeholder {
    background: #e3f2fd !important;
    border: 2px dashed #2196f3 !important;
    height: 60px !important;
    visibility: visible !important;
    display: table-row !important;
}

.dd-slides-table .ui-sortable-placeholder td {
    border: none !important;
    text-align: center !important;
    vertical-align: middle !important;
    color: #2196f3 !important;
    font-weight: 500 !important;
    background: transparent !important;
    padding: 20px !important;
    font-size: 14px !important;
}

/* Error states */
.dd-slide-row.error {
    background-color: #ffeaea;
    border-left: 4px solid #d63638;
}

.dd-slide-row.error td {
    border-bottom-color: #d63638;
}

/* Empty state */
.dd-slides-list:empty::before {
    content: "Brak slajdów. Kliknij 'Dodaj nowy slajd' aby rozpocząć.";
    display: block;
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
    background: #f9f9f9;
    border: 2px dashed #ddd;
    border-radius: 4px;
}

/* Button styles */
.button.button-large {
    padding: 8px 20px;
    font-size: 14px;
    line-height: 1.5;
}

/* Form validation */
.form-table input:invalid {
    border-color: #d63638;
    box-shadow: 0 0 0 1px #d63638;
}

.form-table input:invalid:focus {
    border-color: #d63638;
    box-shadow: 0 0 0 2px rgba(214, 54, 56, 0.2);
}

/* Drag handle */
.dd-slide-header:hover .dashicons-menu {
    color: #0073aa;
}

/* Image upload button states */
.upload-image.uploading {
    opacity: 0.6;
    pointer-events: none;
}

.upload-image.uploading::after {
    content: " Przesyłanie...";
}

/* Accessibility improvements */
.dd-slide-item:focus-within {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
