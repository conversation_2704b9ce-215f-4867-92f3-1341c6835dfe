.woocommerce {
    .woocommerce-MyAccount-navigation {
        width: 20%;

        @media screen and (max-width: 768px) {
            width: 100%;
        }

        ul {
            padding: 0;

            li {
                display: flex;
                list-style: none !important;
                text-align: center;
                border: 1px solid $border-color;
                height: fit-content !important;

                a {
                    padding: 10px 0;
                    width: 100%;
                    text-decoration: none !important;
                    color: $color-black;
                }

                &.is-active {
                    background: $color-primary;

                    a {
                        color: $color-white;
                    }
                }
            }
        }
    }

    .woocommerce-MyAccount-content {
        a {
            color: $link-color;
            text-decoration: none;
        }

        .woocommerce-button {
            background: $color-primary;
            color: $color-white;
        }

        .woocommerce-orders-table__cell.woocommerce-orders-table__cell-order-actions {
            display: flex;
            gap: 10px;
            height: fit-content;
        }
    }

    .unit-price {
        font-weight: 300;
    }
}

.woocommerce-order-overview.woocommerce-thankyou-order-details.order_details {
    padding-left: 0;
}

.woocommerce-order-details {
    a {
        color: $link-color;
        text-decoration: none;
    }
}

.product-tab-content, .tab-panel {
    overflow: hidden;
}

.dd-product-full-description {

    color: var(--text-gray);
    padding: 12px 25px;

    h1, h2, h3, h4, h5, h6 {
        font-family: Lato, sans-serif !important;
        font-weight: 500;
        margin: 20px 0 8px;
    }

    h1 {
        font-size: 20px;
    }

    h2,  h3 {
        font-size: 18px;
    }

    p {
        font-size: 18px !important;
        font-weight: 300;
        line-height: 28px;
        margin: 10px 0 15px;
    }

    strong {
        font-weight: 500;
    }

    a, .entry-content a {
        text-decoration: none;
        color: var(--accent-red);
    }

    a:hover {
        opacity: 0.7;
    }
}

.woocommerce nav.woocommerce-pagination ul li span.current, .woocommerce nav.woocommerce-pagination ul li a:hover, .woocommerce nav.woocommerce-pagination ul li a:focus {
    background: #e9e6ed;
    color: #000000;
}

.dd-omnibus-message-wrapper {
    padding-bottom: 20px;
    font-weight: 300;
}