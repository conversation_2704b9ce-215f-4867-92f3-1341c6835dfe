@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 100;
  font-display: swap;
  src: url(../fonts/Lato-HairlineItalic.woff2) format('woff2');
}

@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src: url(../fonts/Lato-LightItalic.woff2) format('woff2');
}

@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/Lato-Italic.woff2) format('woff2');
}

@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url(../fonts/Lato-BoldItalic.woff2) format('woff2');
}

@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 900;
  font-display: swap;
  src: url(../fonts/Lato-BlackItalic.woff2) format('woff2');
}

@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url(../fonts/Lato-Hairline.woff2) format('woff2');
}

@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(../fonts/Lato-Light.woff2) format('woff2');
}

@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/Lato-Regular.woff2) format('woff2');
}

@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(../fonts/Lato-Bold.woff2) format('woff2');
}

@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url(../fonts/Lato-Black.woff2) format('woff2');
}

@font-face {
  font-family: 'freight-big-pro-light';
  font-style: normal;
  font-weight: normal;
  font-display: swap;
  src: url(../fonts/FreightBigProLight-Regular.woff2) format('woff2');
}

:root {
  --font-base: Lato;
  --font-headings: freight-big-pro-light;
  --font-input: Lato;
}

body, #content, .entry-content, .post-content, .page-content, .post-excerpt, .entry-summary, .entry-excerpt, .widget-area, .widget, .sidebar, #sidebar, footer, .footer, #footer, .site-footer {
  font-family: "Lato";
}

#site-title, .site-title, #site-title a, .site-title a, .entry-title, .entry-title a, h1, h2, h3, h4, h5, h6, .widget-title, .elementor-heading-title {
  font-family: "Lato";
  font-weight: normal;
}

button, .button, input, select, textarea, .wp-block-button, .wp-block-button__link {
  font-family: "Lato";
}

.product_title {
  font-family: "Lato";
}

.woocommerce-product-details__short-description {
  font-family: "Lato";
}

.single-product .price {
  font-family: "freight-big-pro-light";
}

ul.products li.product .woocommerce-loop-product__title {
  font-family: "Lato";
}

ul.products li.product .price {
  font-family: "freight-big-pro-light";
}

ul.products li.product .button {
  font-family: "Lato";
}