jQuery(document).ready(function($) {
    'use strict';

    let slideIndex = $('.dd-slide-row').length;

    // Initialize sortable
    initSortable();

    // Add new slide
    $('#add-slide').on('click', function() {
        addNewSlide();
    });

    // Save all slides
    $('#save-slides').on('click', function() {
        saveAllSlides();
    });

    // Edit slide
    $(document).on('click', '.dd-edit-slide', function() {
        editSlide($(this));
    });

    // Toggle slide status
    $(document).on('click', '.dd-toggle-slide', function() {
        toggleSlide($(this));
    });

    // Delete slide
    $(document).on('click', '.dd-delete-slide', function() {
        deleteSlide($(this));
    });

    // Modal controls
    $(document).on('click', '.dd-modal-close', function() {
        closeModal();
    });

    $(document).on('click', '.dd-modal', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // Save slide from modal
    $(document).on('click', '.dd-save-slide', function() {
        saveSlideFromModal();
    });

    // Image upload
    $(document).on('click', '.upload-image', function() {
        uploadImage($(this));
    });

    // Remove image
    $(document).on('click', '.remove-image', function() {
        removeImage($(this));
    });

    function initSortable() {
        const $tbody = $('#slides-list');
        const $slideRows = $tbody.find('.dd-slide-row');

        if ($tbody.length && $slideRows.length > 0) {
            // Destroy existing sortable if it exists
            if ($tbody.hasClass('ui-sortable')) {
                $tbody.sortable('destroy');
            }

            $tbody.sortable({
                items: '.dd-slide-row',
                handle: '.dd-drag-handle',
                placeholder: 'ui-sortable-placeholder',
                tolerance: 'pointer',
                cursor: 'move',
                opacity: 0.8,
                helper: function(e, tr) {
                    var $originals = tr.children();
                    var $helper = tr.clone();
                    $helper.children().each(function(index) {
                        $(this).width($originals.eq(index).width());
                    });
                    return $helper;
                },
                start: function(event, ui) {
                    ui.placeholder.html('<td colspan="5">Upuść tutaj</td>');
                },
                update: function(event, ui) {
                    updateSlideNumbers();
                    showMessage('Kolejność została zmieniona. Pamiętaj o zapisaniu zmian.', 'success');
                }
            });
        }
    }

    function addNewSlide() {
        const template = $('#slide-template').html();

        if (!template) {
            showMessage('Błąd: nie można znaleźć szablonu slajdu', 'error');
            return;
        }

        const newSlideHtml = template
            .replace(/\{\{INDEX\}\}/g, slideIndex)
            .replace(/\{\{NUMBER\}\}/g, slideIndex + 1);

        const $tbody = $('#slides-list');

        if ($tbody.length) {
            // Remove empty row if it exists
            $tbody.find('.dd-empty-row').remove();

            $tbody.append(newSlideHtml);
            slideIndex++;

            // Reinitialize sortable
            initSortable();

            // Open edit modal for new slide
            const newRow = $tbody.find('tr:last');
            setTimeout(function() {
                editSlide(newRow.find('.dd-edit-slide'));
            }, 100);

            showMessage('Nowy slajd został dodany. Wypełnij wymagane pola.', 'success');
        } else {
            showMessage('Błąd: nie można znaleźć tabeli slajdów', 'error');
        }
    }

    function editSlide($button) {
        const $row = $button.closest('.dd-slide-row');
        const index = $row.data('index');

        const slideData = {
            image: $row.find('.slide-image').val(),
            mobile_image: $row.find('.slide-mobile-image').val(),
            alt: $row.find('.slide-alt').val(),
            link: $row.find('.slide-link').val(),
            active: $row.find('.slide-active').val() === '1'
        };

        // Load edit form via AJAX
        $.post(ddSlides.ajaxUrl, {
            action: 'dd_get_slide_edit_form',
            nonce: ddSlides.nonce,
            slide: slideData,
            index: index
        }, function(response) {
            if (response.success) {
                $('#slide-edit-modal .dd-modal-body').html(response.data.html);
                $('#slide-edit-modal').data('slide-index', index).show();
            }
        });
    }

    function toggleSlide($button) {
        const $row = $button.closest('.dd-slide-row');
        const $activeInput = $row.find('.slide-active');
        const $statusCell = $row.find('.column-status');
        const $toggleIcon = $button.find('.dashicons');

        const isActive = $activeInput.val() === '1';
        const newStatus = !isActive;

        $activeInput.val(newStatus ? '1' : '0');

        // Update status display
        if (newStatus) {
            $statusCell.html('<div class="slide-status"><span class="status-active"><span class="dashicons dashicons-yes-alt"></span>Aktywny</span></div>');
            $toggleIcon.removeClass('dashicons-visibility').addClass('dashicons-hidden');
            $button.attr('title', 'Dezaktywuj');
        } else {
            $statusCell.html('<div class="slide-status"><span class="status-inactive"><span class="dashicons dashicons-dismiss"></span>Nieaktywny</span></div>');
            $toggleIcon.removeClass('dashicons-hidden').addClass('dashicons-visibility');
            $button.attr('title', 'Aktywuj');
        }

        showMessage('Status slajdu został zmieniony. Pamiętaj o zapisaniu zmian.', 'success');
    }

    function deleteSlide($button) {
        if (!confirm(ddSlides.confirmDelete)) {
            return;
        }

        const $row = $button.closest('.dd-slide-row');

        $row.fadeOut(300, function() {
            $(this).remove();
            updateSlideNumbers();

            // Check if no slides left
            if ($('#slides-list .dd-slide-row').length === 0) {
                // Destroy sortable if it exists
                const $tbody = $('#slides-list');
                if ($tbody.hasClass('ui-sortable')) {
                    $tbody.sortable('destroy');
                }

                // Add empty state row
                $tbody.html(`
                    <tr class="dd-empty-row">
                        <td colspan="5" class="dd-empty-state">
                            <div class="dd-empty-icon">
                                <span class="dashicons dashicons-images-alt2"></span>
                            </div>
                            <h3>Brak slajdów</h3>
                            <p>Kliknij "Dodaj nowy slajd" aby rozpocząć tworzenie bannerów na stronie głównej.</p>
                        </td>
                    </tr>
                `);

                // Reset slideIndex
                slideIndex = 0;
            }

            showMessage('Slajd został usunięty', 'success');
        });
    }

    function closeModal() {
        $('#slide-edit-modal').hide();
    }

    function saveSlideFromModal() {
        const $modal = $('#slide-edit-modal');
        const index = $modal.data('slide-index');
        const $form = $modal.find('.dd-slide-edit-form');
        const $row = $(`.dd-slide-row[data-index="${index}"]`);

        // Validate required fields
        let hasErrors = false;
        $form.find('input[required]').each(function() {
            if (!$(this).val()) {
                hasErrors = true;
                $(this).addClass('error');
            } else {
                $(this).removeClass('error');
            }
        });

        if (hasErrors) {
            showMessage('Wypełnij wszystkie wymagane pola', 'error');
            return;
        }

        // Update hidden inputs in table row
        $row.find('.slide-image').val($form.find('input[name="image"]').val());
        $row.find('.slide-mobile-image').val($form.find('input[name="mobile_image"]').val());
        $row.find('.slide-alt').val($form.find('input[name="alt"]').val());
        $row.find('.slide-link').val($form.find('input[name="link"]').val());
        $row.find('.slide-active').val($form.find('input[name="active"]').is(':checked') ? '1' : '0');

        // Update table display
        updateRowDisplay($row, {
            image: $form.find('input[name="image"]').val(),
            mobile_image: $form.find('input[name="mobile_image"]').val(),
            alt: $form.find('input[name="alt"]').val(),
            link: $form.find('input[name="link"]').val(),
            active: $form.find('input[name="active"]').is(':checked')
        });

        closeModal();
        showMessage('Slajd został zaktualizowany. Pamiętaj o zapisaniu zmian.', 'success');
    }

    function updateSlideNumbers() {
        $('#slides-list .dd-slide-row').each(function(index) {
            $(this).find('.slide-number').text(index + 1);

            // Update form field names
            $(this).find('input').each(function() {
                const name = $(this).attr('name');
                if (name) {
                    const newName = name.replace(/\[(\d+)\]/, '[' + index + ']');
                    $(this).attr('name', newName);
                }
            });

            // Update data-index
            $(this).attr('data-index', index);
        });

        // Update global slideIndex
        slideIndex = $('#slides-list .dd-slide-row').length;
    }

    function updateRowDisplay($row, slideData) {
        // Update preview images
        const $preview = $row.find('.dd-slide-preview');
        let previewHtml = '';

        if (slideData.image) {
            previewHtml += `<img src="${slideData.image}" alt="${slideData.alt}" class="preview-desktop">`;
        } else {
            previewHtml += '<div class="preview-placeholder"><span class="dashicons dashicons-format-image"></span><span>Brak zdjęcia</span></div>';
        }

        if (slideData.mobile_image) {
            previewHtml += `<img src="${slideData.mobile_image}" alt="${slideData.alt}" class="preview-mobile">`;
            previewHtml += '<span class="mobile-indicator">Mobile</span>';
        }

        $preview.html(previewHtml);

        // Update details
        $row.find('.slide-alt').text(slideData.alt || 'Brak tekstu alternatywnego');
        $row.find('.slide-link-display code').text(slideData.link || 'Brak linku');

        // Update status
        const $statusCell = $row.find('.column-status');
        const $toggleButton = $row.find('.dd-toggle-slide');
        const $toggleIcon = $toggleButton.find('.dashicons');

        if (slideData.active) {
            $statusCell.html('<div class="slide-status"><span class="status-active"><span class="dashicons dashicons-yes-alt"></span>Aktywny</span></div>');
            $toggleIcon.removeClass('dashicons-visibility').addClass('dashicons-hidden');
            $toggleButton.attr('title', 'Dezaktywuj');
        } else {
            $statusCell.html('<div class="slide-status"><span class="status-inactive"><span class="dashicons dashicons-dismiss"></span>Nieaktywny</span></div>');
            $toggleIcon.removeClass('dashicons-hidden').addClass('dashicons-visibility');
            $toggleButton.attr('title', 'Aktywuj');
        }
    }

    function saveAllSlides() {
        const $button = $('#save-slides');
        const originalText = $button.text();

        // Validate required fields
        let hasErrors = false;
        $('.dd-slide-row').each(function() {
            const $row = $(this);
            const image = $row.find('.slide-image').val();
            const alt = $row.find('.slide-alt').val();
            const link = $row.find('.slide-link').val();

            if (!image || !alt || !link) {
                hasErrors = true;
                $row.addClass('error');
            } else {
                $row.removeClass('error');
            }
        });

        if (hasErrors) {
            showMessage('Niektóre slajdy mają niekompletne dane. Sprawdź wszystkie slajdy i wypełnij wymagane pola.', 'error');
            return;
        }

        $button.prop('disabled', true).text('Zapisywanie...');

        const formData = new FormData();
        formData.append('action', 'dd_save_slides');
        formData.append('nonce', ddSlides.nonce);

        // Collect all slide data
        const slides = [];
        $('.dd-slide-row').each(function() {
            const $row = $(this);
            const slideData = {
                image: $row.find('.slide-image').val(),
                mobile_image: $row.find('.slide-mobile-image').val(),
                alt: $row.find('.slide-alt').val(),
                link: $row.find('.slide-link').val(),
                active: $row.find('.slide-active').val() === '1' ? 1 : 0
            };
            slides.push(slideData);
        });

        // Add slides to form data
        slides.forEach(function(slide, index) {
            Object.keys(slide).forEach(function(key) {
                formData.append(`slides[${index}][${key}]`, slide[key]);
            });
        });

        $.ajax({
            url: ddSlides.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    $('.dd-slide-row').removeClass('error');
                } else {
                    showMessage(response.data.message || 'Wystąpił błąd podczas zapisywania', 'error');
                }
            },
            error: function() {
                showMessage('Wystąpił błąd podczas zapisywania', 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    }

    function uploadImage($button) {
        const $container = $button.closest('.dd-image-upload');
        const $input = $container.find('.image-url');
        const $preview = $container.find('.image-preview');
        const $removeBtn = $container.find('.remove-image');
        
        // Open WordPress media library
        const mediaUploader = wp.media({
            title: 'Wybierz zdjęcie dla slajdu',
            button: {
                text: 'Wybierz to zdjęcie'
            },
            multiple: false,
            library: {
                type: 'image'
            }
        });
        
        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();
            
            $input.val(attachment.url);
            $preview.html('<img src="' + attachment.url + '" alt="Preview">');
            $removeBtn.show();
            
            showMessage('Zdjęcie zostało wybrane', 'success');
        });
        
        mediaUploader.open();
    }

    function removeImage($button) {
        const $container = $button.closest('.dd-image-upload');
        const $input = $container.find('.image-url');
        const $preview = $container.find('.image-preview');
        
        $input.val('');
        $preview.empty();
        $button.hide();
        
        showMessage('Zdjęcie zostało usunięte', 'success');
    }

    function showMessage(message, type) {
        // Remove existing messages
        $('.dd-message').remove();
        
        const $message = $('<div class="dd-message ' + type + '">' + message + '</div>');
        $('.dd-slides-container').prepend($message);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $message.fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
        
        // Scroll to message
        $('html, body').animate({
            scrollTop: $message.offset().top - 100
        }, 300);
    }

    // Form validation
    $(document).on('input', 'input[required]', function() {
        if ($(this).val()) {
            $(this).removeClass('error');
        }
    });

    // Auto-save draft (optional feature)
    let autoSaveTimeout;
    $(document).on('input change', '.dd-slide-item input, .dd-slide-item select, .dd-slide-item textarea', function() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(function() {
            // Could implement auto-save to drafts here
        }, 2000);
    });

    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Ctrl+S to save
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveAllSlides();
        }
        
        // Ctrl+N to add new slide
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            addNewSlide();
        }
    });

    // Confirm before leaving if there are unsaved changes
    let hasUnsavedChanges = false;
    
    $(document).on('input change', '.dd-slide-item input, .dd-slide-item select, .dd-slide-item textarea', function() {
        hasUnsavedChanges = true;
    });
    
    $('#save-slides').on('click', function() {
        hasUnsavedChanges = false;
    });
    
    $(window).on('beforeunload', function() {
        if (hasUnsavedChanges) {
            return 'Masz niezapisane zmiany. Czy na pewno chcesz opuścić stronę?';
        }
    });

    // Initialize tooltips or help text
    $('.description').each(function() {
        $(this).attr('title', $(this).text());
    });
});
