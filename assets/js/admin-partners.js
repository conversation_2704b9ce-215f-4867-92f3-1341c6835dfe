jQuery(document).ready(function($) {
    'use strict';

    let partnerIndex = $('.dd-partner-row').length;

    // Initialize sortable
    initSortable();

    // Add new partner
    $('#add-partner').on('click', function() {
        addNewPartner();
    });

    // Save all partners
    $('#save-partners').on('click', function() {
        saveAllPartners();
    });

    // Edit partner
    $(document).on('click', '.dd-edit-partner', function() {
        editPartner($(this));
    });

    // Toggle partner status
    $(document).on('click', '.dd-toggle-partner', function() {
        togglePartner($(this));
    });

    // Delete partner
    $(document).on('click', '.dd-delete-partner', function() {
        deletePartner($(this));
    });

    // Modal controls
    $(document).on('click', '.dd-modal-close', function() {
        closeModal();
    });

    $(document).on('click', '.dd-modal', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // Save partner from modal
    $(document).on('click', '.dd-save-partner', function() {
        savePartnerFromModal();
    });

    // Image upload
    $(document).on('click', '.upload-image', function() {
        uploadImage($(this));
    });

    // Remove image
    $(document).on('click', '.remove-image', function() {
        removeImage($(this));
    });

    function initSortable() {
        const $tbody = $('#partners-list');
        const $partnerRows = $tbody.find('.dd-partner-row');

        if ($tbody.length && $partnerRows.length > 0) {
            // Destroy existing sortable if it exists
            if ($tbody.hasClass('ui-sortable')) {
                $tbody.sortable('destroy');
            }

            $tbody.sortable({
                items: '.dd-partner-row',
                handle: '.dd-drag-handle',
                placeholder: 'ui-sortable-placeholder',
                tolerance: 'pointer',
                cursor: 'move',
                opacity: 0.8,
                helper: function(e, tr) {
                    var $originals = tr.children();
                    var $helper = tr.clone();
                    $helper.children().each(function(index) {
                        $(this).width($originals.eq(index).width());
                    });
                    return $helper;
                },
                start: function(event, ui) {
                    ui.placeholder.html('<td colspan="5">Upuść tutaj</td>');
                },
                update: function(event, ui) {
                    updatePartnerNumbers();
                    showMessage('Kolejność została zmieniona. Pamiętaj o zapisaniu zmian.', 'success');
                }
            });
        }
    }

    function addNewPartner() {
        const template = $('#partner-template').html();

        if (!template) {
            showMessage('Błąd: nie można znaleźć szablonu partnera', 'error');
            return;
        }

        const newPartnerHtml = template
            .replace(/\{\{INDEX\}\}/g, partnerIndex)
            .replace(/\{\{NUMBER\}\}/g, partnerIndex + 1);

        const $tbody = $('#partners-list');

        if ($tbody.length) {
            // Remove empty row if it exists
            $tbody.find('.dd-empty-row').remove();

            $tbody.append(newPartnerHtml);
            partnerIndex++;

            // Reinitialize sortable
            initSortable();

            // Open edit modal for new partner
            const newRow = $tbody.find('tr:last');
            setTimeout(function() {
                editPartner(newRow.find('.dd-edit-partner'));
            }, 100);

            showMessage('Nowy partner został dodany. Wypełnij wymagane pola.', 'success');
        } else {
            showMessage('Błąd: nie można znaleźć tabeli partnerów', 'error');
        }
    }

    function editPartner($button) {
        const $row = $button.closest('.dd-partner-row');
        const index = $row.data('index');

        const partnerData = {
            image: $row.find('.partner-image').val(),
            alt: $row.find('.partner-alt').val(),
            active: $row.find('.partner-active').val() === '1'
        };

        // Load edit form via AJAX
        $.post(ddPartners.ajaxUrl, {
            action: 'dd_get_partner_edit_form',
            nonce: ddPartners.nonce,
            partner: partnerData,
            index: index
        }, function(response) {
            if (response.success) {
                $('#partner-edit-modal .dd-modal-body').html(response.data.html);
                $('#partner-edit-modal').data('partner-index', index).show();
            }
        });
    }

    function togglePartner($button) {
        const $row = $button.closest('.dd-partner-row');
        const $activeInput = $row.find('.partner-active');
        const $statusCell = $row.find('.column-status');
        const $toggleIcon = $button.find('.dashicons');

        const isActive = $activeInput.val() === '1';
        const newStatus = !isActive;

        $activeInput.val(newStatus ? '1' : '0');

        // Update status display
        if (newStatus) {
            $statusCell.html('<div class="partner-status"><span class="status-active"><span class="dashicons dashicons-yes-alt"></span>Aktywny</span></div>');
            $toggleIcon.removeClass('dashicons-visibility').addClass('dashicons-hidden');
            $button.attr('title', 'Dezaktywuj');
        } else {
            $statusCell.html('<div class="partner-status"><span class="status-inactive"><span class="dashicons dashicons-dismiss"></span>Nieaktywny</span></div>');
            $toggleIcon.removeClass('dashicons-hidden').addClass('dashicons-visibility');
            $button.attr('title', 'Aktywuj');
        }

        showMessage('Status partnera został zmieniony. Pamiętaj o zapisaniu zmian.', 'success');
    }

    function deletePartner($button) {
        if (!confirm(ddPartners.confirmDelete)) {
            return;
        }

        const $row = $button.closest('.dd-partner-row');

        $row.fadeOut(300, function() {
            $(this).remove();
            updatePartnerNumbers();

            // Check if no partners left
            if ($('#partners-list .dd-partner-row').length === 0) {
                // Destroy sortable if it exists
                const $tbody = $('#partners-list');
                if ($tbody.hasClass('ui-sortable')) {
                    $tbody.sortable('destroy');
                }

                // Add empty state row
                $tbody.html(`
                    <tr class="dd-empty-row">
                        <td colspan="5" class="dd-empty-state">
                            <div class="dd-empty-icon">
                                <span class="dashicons dashicons-businessman"></span>
                            </div>
                            <h3>Brak logo partnerów</h3>
                            <p>Kliknij "Dodaj nowe logo" aby rozpocząć dodawanie logo partnerów.</p>
                        </td>
                    </tr>
                `);

                // Reset partnerIndex
                partnerIndex = 0;
            }

            showMessage('Logo partnera zostało usunięte', 'success');
        });
    }

    function closeModal() {
        $('#partner-edit-modal').hide();
    }

    function savePartnerFromModal() {
        const $modal = $('#partner-edit-modal');
        const index = $modal.data('partner-index');
        const $form = $modal.find('.dd-partner-edit-form');
        const $row = $(`.dd-partner-row[data-index="${index}"]`);

        // Validate required fields
        let hasErrors = false;
        $form.find('input[required]').each(function() {
            if (!$(this).val()) {
                hasErrors = true;
                $(this).addClass('error');
            } else {
                $(this).removeClass('error');
            }
        });

        if (hasErrors) {
            showMessage('Wypełnij wszystkie wymagane pola', 'error');
            return;
        }

        // Update hidden inputs in table row
        $row.find('.partner-image').val($form.find('input[name="image"]').val());
        $row.find('.partner-alt').val($form.find('input[name="alt"]').val());
        $row.find('.partner-active').val($form.find('input[name="active"]').is(':checked') ? '1' : '0');

        // Update table display
        updateRowDisplay($row, {
            image: $form.find('input[name="image"]').val(),
            alt: $form.find('input[name="alt"]').val(),
            active: $form.find('input[name="active"]').is(':checked')
        });

        closeModal();
        showMessage('Partner został zaktualizowany. Pamiętaj o zapisaniu zmian.', 'success');
    }

    function updatePartnerNumbers() {
        $('#partners-list .dd-partner-row').each(function(index) {
            $(this).find('.partner-number').text(index + 1);

            // Update form field names
            $(this).find('input').each(function() {
                const name = $(this).attr('name');
                if (name) {
                    const newName = name.replace(/\[(\d+)\]/, '[' + index + ']');
                    $(this).attr('name', newName);
                }
            });

            // Update data-index
            $(this).attr('data-index', index);
        });

        // Update global partnerIndex
        partnerIndex = $('#partners-list .dd-partner-row').length;
    }

    function updateRowDisplay($row, partnerData) {
        // Update preview image
        const $preview = $row.find('.dd-partner-preview');
        let previewHtml = '';

        if (partnerData.image) {
            previewHtml = `<img src="${partnerData.image}" alt="${partnerData.alt}" class="preview-logo">`;
        } else {
            previewHtml = '<div class="preview-placeholder"><span class="dashicons dashicons-format-image"></span><span>Brak logo</span></div>';
        }

        $preview.html(previewHtml);

        // Update details
        $row.find('.partner-alt-display').text(partnerData.alt || 'Brak tekstu alternatywnego');

        // Update status
        const $statusCell = $row.find('.column-status');
        const $toggleButton = $row.find('.dd-toggle-partner');
        const $toggleIcon = $toggleButton.find('.dashicons');

        if (partnerData.active) {
            $statusCell.html('<div class="partner-status"><span class="status-active"><span class="dashicons dashicons-yes-alt"></span>Aktywny</span></div>');
            $toggleIcon.removeClass('dashicons-visibility').addClass('dashicons-hidden');
            $toggleButton.attr('title', 'Dezaktywuj');
        } else {
            $statusCell.html('<div class="partner-status"><span class="status-inactive"><span class="dashicons dashicons-dismiss"></span>Nieaktywny</span></div>');
            $toggleIcon.removeClass('dashicons-hidden').addClass('dashicons-visibility');
            $toggleButton.attr('title', 'Aktywuj');
        }
    }

    function saveAllPartners() {
        const $button = $('#save-partners');
        const originalText = $button.text();

        // Validate required fields
        let hasErrors = false;
        $('.dd-partner-row').each(function() {
            const $row = $(this);
            const image = $row.find('.partner-image').val();
            const alt = $row.find('.partner-alt').val();

            if (!image || !alt) {
                hasErrors = true;
                $row.addClass('error');
            } else {
                $row.removeClass('error');
            }
        });

        if (hasErrors) {
            showMessage('Niektórzy partnerzy mają niekompletne dane. Sprawdź wszystkich partnerów i wypełnij wymagane pola.', 'error');
            return;
        }

        $button.prop('disabled', true).text('Zapisywanie...');

        const formData = new FormData();
        formData.append('action', 'dd_save_partners');
        formData.append('nonce', ddPartners.nonce);

        // Collect all partner data
        const partners = [];
        $('.dd-partner-row').each(function() {
            const $row = $(this);
            const partnerData = {
                image: $row.find('.partner-image').val(),
                alt: $row.find('.partner-alt').val(),
                active: $row.find('.partner-active').val() === '1' ? 1 : 0
            };
            partners.push(partnerData);
        });

        // Add partners to form data
        partners.forEach(function(partner, index) {
            Object.keys(partner).forEach(function(key) {
                formData.append(`partners[${index}][${key}]`, partner[key]);
            });
        });

        $.ajax({
            url: ddPartners.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    $('.dd-partner-row').removeClass('error');
                } else {
                    showMessage(response.data.message || 'Wystąpił błąd podczas zapisywania', 'error');
                }
            },
            error: function() {
                showMessage('Wystąpił błąd podczas zapisywania', 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    }

    function uploadImage($button) {
        const $container = $button.closest('.dd-image-upload');
        const $input = $container.find('.image-url');
        const $preview = $container.find('.image-preview');
        const $removeBtn = $container.find('.remove-image');

        // Open WordPress media library
        const mediaUploader = wp.media({
            title: 'Wybierz logo partnera',
            button: {
                text: 'Wybierz to logo'
            },
            multiple: false,
            library: {
                type: 'image'
            }
        });

        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();

            $input.val(attachment.url);
            $preview.html('<img src="' + attachment.url + '" alt="Preview">');
            $removeBtn.show();

            showMessage('Logo zostało wybrane', 'success');
        });

        mediaUploader.open();
    }

    function removeImage($button) {
        const $container = $button.closest('.dd-image-upload');
        const $input = $container.find('.image-url');
        const $preview = $container.find('.image-preview');

        $input.val('');
        $preview.empty();
        $button.hide();

        showMessage('Logo zostało usunięte', 'success');
    }

    function showMessage(message, type) {
        // Remove existing messages
        $('.dd-message').remove();

        const $message = $('<div class="dd-message ' + type + '">' + message + '</div>');
        $('.dd-partners-container').prepend($message);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            $message.fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);

        // Scroll to message
        $('html, body').animate({
            scrollTop: $message.offset().top - 100
        }, 300);
    }

    // Form validation
    $(document).on('input', 'input[required]', function() {
        if ($(this).val()) {
            $(this).removeClass('error');
        }
    });

    // Confirm before leaving if there are unsaved changes
    let hasUnsavedChanges = false;

    $(document).on('input change', '.dd-partner-item input, .dd-partner-item select, .dd-partner-item textarea', function() {
        hasUnsavedChanges = true;
    });

    $('#save-partners').on('click', function() {
        hasUnsavedChanges = false;
    });

    $(window).on('beforeunload', function() {
        if (hasUnsavedChanges) {
            return 'Masz niezapisane zmiany. Czy na pewno chcesz opuścić stronę?';
        }
    });
});
